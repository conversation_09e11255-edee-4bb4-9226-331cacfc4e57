'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import useEmblaCarousel from 'embla-carousel-react';
import {
  Star,
  MapPin,
  Package,
  Users,
  ArrowRight,
  Crown,
  Store,
  CheckCircle2,
  ShoppingBag,
  ChevronLeft,
  ChevronRight,
  ShoppingCart,
  Eye,
  Phone,
  Globe,
  Mail
} from 'lucide-react';

interface FeaturedVendor {
  id: string;
  businessName: string;
  businessType: string;
  description?: string;
  city: string;
  province: string;
  logo?: string;
  bannerImage?: string;
  rating?: number;
  totalReviews: number;
  isVerified: boolean;
  featured: boolean;
  totalProducts: number;
  totalSales: number;
  contactPhone?: string;
  website?: string;
  email?: string;
  acceptsNfcPayments?: boolean;
  productCategories: string[] | string;
  sampleProducts: Array<{
    id: string;
    name: string;
    description?: string;
    price: number;
    originalPrice?: number;
    isOnSale?: boolean;
    currency: string;
    imageUrl?: string;
    category?: string;
  }>;
}

const formatPrice = (price: number, currency: string = 'ZMW') => {
  return new Intl.NumberFormat('en-ZM', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
};

export default function FeaturedVendorsSection() {
  const [vendors, setVendors] = useState<FeaturedVendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    align: 'start',
    slidesToScroll: 1,
    containScroll: 'trimSnaps',
    dragFree: true,
    breakpoints: {
      '(min-width: 768px)': { slidesToScroll: 1 },
      '(min-width: 1024px)': { slidesToScroll: 1 }
    }
  });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    const fetchFeaturedVendors = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/vendors/featured?limit=12');

        if (!response.ok) {
          throw new Error('Failed to fetch featured vendors');
        }

        const data = await response.json();
        setVendors(data.vendors || []);
      } catch (err) {
        console.error('Error fetching featured vendors:', err);
        setError('Failed to load featured vendors');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedVendors();
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Skeleton className="h-10 w-64 mx-auto mb-4" />
            <Skeleton className="h-6 w-96 mx-auto" />
          </div>
          <div className="relative -mx-4">
            <div className="flex gap-6 overflow-hidden px-4 pb-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="flex-shrink-0 w-[400px] min-w-[400px] overflow-hidden">
                  <Skeleton className="h-32 w-full" />
                  <CardContent className="p-4 space-y-3">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-3 w-full" />
                    <div className="grid grid-cols-2 gap-2">
                      <Skeleton className="h-12 w-full" />
                      <Skeleton className="h-12 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (error || vendors.length === 0) {
    return (
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <Store className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {error ? 'Unable to Load Featured Vendors' : 'No Featured Vendors'}
          </h3>
          <p className="text-gray-500">
            {error || 'Check back later for amazing featured vendors and their products.'}
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Crown className="h-8 w-8 text-yellow-500" />
            <h2 className="text-4xl font-bold text-gray-900">Featured Vendors</h2>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover top-rated vendors offering amazing products and services. Shop from verified businesses you can trust.
          </p>
        </div>

        {/* Vendors Carousel */}
        <div className="relative">
          {/* Left Navigation Arrow */}
          <Button
            variant="outline"
            size="sm"
            onClick={scrollPrev}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-20 h-12 w-12 rounded-full p-0 bg-white/90 backdrop-blur-sm border-gray-200 shadow-lg hover:bg-white hover:shadow-xl transition-all duration-200 hidden md:flex items-center justify-center"
          >
            <ChevronLeft className="h-5 w-5" />
          </Button>

          {/* Right Navigation Arrow */}
          <Button
            variant="outline"
            size="sm"
            onClick={scrollNext}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-20 h-12 w-12 rounded-full p-0 bg-white/90 backdrop-blur-sm border-gray-200 shadow-lg hover:bg-white hover:shadow-xl transition-all duration-200 hidden md:flex items-center justify-center"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>

          {/* Gradient fade on right to indicate more content */}
          <div className="absolute right-12 top-0 bottom-0 w-16 bg-gradient-to-l from-white to-transparent z-10 pointer-events-none hidden md:block"></div>

          {/* Gradient fade on left to indicate more content */}
          <div className="absolute left-12 top-0 bottom-0 w-16 bg-gradient-to-r from-white to-transparent z-10 pointer-events-none hidden md:block"></div>

          <div className="overflow-hidden px-4 md:px-16" ref={emblaRef}>
            <div className="flex gap-6 pb-4">
          {vendors.map((vendor) => (
            <Card key={vendor.id} className="flex-shrink-0 w-[320px] min-w-[320px] sm:w-[300px] sm:min-w-[300px] xs:w-[280px] xs:min-w-[280px] group hover:shadow-lg transition-all duration-300 overflow-hidden border-0 shadow-md bg-white">
              {/* Vendor Banner */}
              <div className="relative h-24 bg-gradient-to-br from-blue-500 to-purple-600">
                {vendor.bannerImage ? (
                  <Image
                    src={vendor.bannerImage}
                    alt={`${vendor.businessName} banner`}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600" />
                )}
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/20" />
                
                {/* Featured Badge */}
                <div className="absolute top-4 left-4">
                  <Badge className="bg-yellow-500 text-white font-medium">
                    <Crown className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                </div>

                {/* Business Type Badge */}
                <div className="absolute top-4 right-4 flex flex-col gap-2">
                  <Badge variant="secondary" className="bg-white/90 text-gray-900">
                    {vendor.businessType}
                  </Badge>
                  {vendor.acceptsNfcPayments && (
                    <Badge className="bg-green-600 text-white text-xs flex items-center gap-1">
                      📱 NFC Pay
                    </Badge>
                  )}
                </div>

                {/* Vendor Logo */}
                <div className="absolute -bottom-4 left-4">
                  <div className="w-8 h-8 rounded-full border-2 border-white bg-white overflow-hidden shadow-md">
                    {vendor.logo ? (
                      <Image
                        src={vendor.logo}
                        alt={vendor.businessName}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                        {vendor.businessName.charAt(0)}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <CardContent className="pt-6 p-4">
                {/* Vendor Info */}
                <div className="space-y-2">
                  {/* Name and Verification */}
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-bold text-base text-gray-900 line-clamp-1">
                        {vendor.businessName}
                      </h3>
                      {vendor.isVerified && (
                        <CheckCircle2 className="h-4 w-4 text-blue-500" />
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <MapPin className="h-3 w-3" />
                      <span>{vendor.city}, {vendor.province}</span>
                    </div>
                  </div>

                  {/* Description */}
                  {vendor.description && (
                    <p className="text-gray-600 text-xs line-clamp-1">
                      {vendor.description}
                    </p>
                  )}

                  {/* Rating and Stats - Combined */}
                  <div className="flex items-center justify-between">
                    {vendor.rating && vendor.rating > 0 ? (
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        <span className="text-xs font-medium">
                          {vendor.rating.toFixed(1)}
                        </span>
                        <span className="text-xs text-gray-500">
                          ({vendor.totalReviews})
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <Package className="h-3 w-3" />
                        <span>{vendor.totalProducts} products</span>
                      </div>
                    )}

                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <ShoppingBag className="h-3 w-3" />
                      <span>{vendor.totalSales} sales</span>
                    </div>
                  </div>

                  {/* Product Categories */}
                  {vendor.productCategories && Array.isArray(vendor.productCategories) && vendor.productCategories.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {vendor.productCategories.slice(0, 3).map((category, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {category}
                        </Badge>
                      ))}
                      {vendor.productCategories.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{vendor.productCategories.length - 3} more
                        </Badge>
                      )}
                    </div>
                  ) : vendor.productCategories && typeof vendor.productCategories === 'string' ? (
                    <div className="flex flex-wrap gap-1">
                      <Badge variant="outline" className="text-xs">
                        {vendor.productCategories}
                      </Badge>
                    </div>
                  ) : null}

                  {/* Sample Products - Only Real Images */}
                  {vendor.sampleProducts && vendor.sampleProducts.length > 0 ? (
                    <div>
                      <h4 className="text-sm font-semibold mb-3 text-gray-800 flex items-center gap-2">
                        <Package className="h-4 w-4 text-blue-600" />
                        Featured Products
                      </h4>
                      <div className={`grid gap-2 ${vendor.sampleProducts.length === 1 ? 'grid-cols-1' : 'grid-cols-2'}`}>
                        {vendor.sampleProducts.slice(0, 2).map((product) => (
                          <div key={product.id} className="bg-white rounded-md border border-gray-200 overflow-hidden hover:shadow-sm transition-all duration-200 group/product cursor-pointer">
                            {/* Product Image */}
                            <div className="aspect-square bg-gray-50 overflow-hidden relative h-20">
                              {product.imageUrl ? (
                                <Image
                                  src={product.imageUrl}
                                  alt={product.name}
                                  width={80}
                                  height={80}
                                  className="w-full h-full object-cover group-hover/product:scale-105 transition-transform duration-200"
                                />
                              ) : (
                                <div className="w-full h-full bg-gradient-to-br from-blue-100 to-purple-100 flex items-center justify-center">
                                  <Package className="h-8 w-8 text-blue-500" />
                                </div>
                              )}

                              {/* Hover Overlay */}
                              <div className="absolute inset-0 bg-black/0 group-hover/product:bg-black/10 transition-colors duration-200" />

                              {/* Sale Badge */}
                              {product.isOnSale && (
                                <div className="absolute top-2 left-2">
                                  <Badge className="bg-red-500 text-white text-xs font-bold">
                                    SALE
                                  </Badge>
                                </div>
                              )}

                              {/* Quick View Button */}
                              <div className="absolute top-2 right-2 opacity-0 group-hover/product:opacity-100 transition-opacity duration-200">
                                <Button size="sm" variant="secondary" className="h-7 w-7 p-0 rounded-full bg-white/90 hover:bg-white">
                                  <Eye className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>

                            {/* Product Info */}
                            <div className="p-2">
                              <h5 className="text-xs font-medium text-gray-900 line-clamp-1 mb-1 leading-tight" title={product.name}>
                                {product.name}
                              </h5>

                              <div className="flex items-center justify-between">
                                <div className="flex flex-col">
                                  {product.isOnSale && product.originalPrice ? (
                                    <div className="flex items-center gap-1">
                                      <span className="text-sm font-bold text-green-600">
                                        {formatPrice(product.price, product.currency)}
                                      </span>
                                      <span className="text-xs text-gray-500 line-through">
                                        {formatPrice(product.originalPrice, product.currency)}
                                      </span>
                                    </div>
                                  ) : (
                                    <span className="text-sm font-bold text-green-600">
                                      {formatPrice(product.price, product.currency)}
                                    </span>
                                  )}
                                </div>

                                <Button size="sm" className="h-6 px-2 text-xs group-hover/product:scale-105 transition-transform">
                                  <ShoppingCart className="h-2.5 w-2.5 mr-1" />
                                  {vendor.acceptsNfcPayments ? '📱' : 'Add'}
                                </Button>
                              </div>

                              {/* Product Rating */}
                              <div className="flex items-center gap-1 mt-1">
                                <div className="flex items-center">
                                  {[...Array(5)].map((_, i) => (
                                    <Star key={i} className={`h-2.5 w-2.5 ${i < 4 ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`} />
                                  ))}
                                </div>
                                <span className="text-xs text-gray-500">(4.0)</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* View More Products */}
                      <div className="mt-3 text-center">
                        <Button variant="ghost" size="sm" className="text-xs text-blue-600 hover:text-blue-700">
                          <Eye className="h-3 w-3 mr-1" />
                          View all {vendor.totalProducts || 12} products
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <Package className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-2">No featured products available</p>
                      <Button variant="ghost" size="sm" className="text-xs text-blue-600 hover:text-blue-700">
                        View all products
                      </Button>
                    </div>
                  )}

                  {/* Contact Info */}
                  {(vendor.contactPhone || vendor.website || vendor.email || vendor.acceptsNfcPayments) && (
                    <div className="border-t pt-2 space-y-2">
                      {/* Contact Links */}
                      {(vendor.contactPhone || vendor.website || vendor.email) && (
                        <div className="flex items-center gap-3 text-xs text-gray-500">
                          {vendor.contactPhone && (
                            <a href={`tel:${vendor.contactPhone}`} className="flex items-center gap-1 hover:text-blue-600 transition-colors">
                              <Phone className="h-3 w-3" />
                              <span className="truncate">{vendor.contactPhone}</span>
                            </a>
                          )}
                          {vendor.website && (
                            <a href={vendor.website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-1 hover:text-blue-600 transition-colors">
                              <Globe className="h-3 w-3" />
                              <span>Website</span>
                            </a>
                          )}
                          {vendor.email && (
                            <a href={`mailto:${vendor.email}`} className="flex items-center gap-1 hover:text-blue-600 transition-colors">
                              <Mail className="h-3 w-3" />
                              <span>Email</span>
                            </a>
                          )}
                        </div>
                      )}

                      {/* Payment Methods */}
                      {vendor.acceptsNfcPayments && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs text-gray-600">Payment:</span>
                          <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                            📱 NFC Payments
                          </Badge>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Action Button */}
                  <Link href={`/vendors/${vendor.id}`}>
                    <Button size="sm" className="w-full group">
                      {vendor.acceptsNfcPayments ? '📱 Visit Store' : 'Visit Store'}
                      <ArrowRight className="h-3 w-3 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="flex md:hidden justify-center gap-4 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={scrollPrev}
              className="h-12 w-12 rounded-full p-0 bg-white shadow-lg border-gray-200"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-full">
              <span className="text-sm text-gray-600">Swipe to explore</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={scrollNext}
              className="h-12 w-12 rounded-full p-0 bg-white shadow-lg border-gray-200"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link href="/vendors">
            <Button variant="outline" size="lg" className="group">
              View All Vendors
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
