"use client";
import React, { useState, useEffect } from "react";
import { UserButton } from "@/components/auth/user-button";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import logo from "../../../../public/images/quick.png";
import { OrderTicketCounts } from "./order-ticket-counts";
import {
  CalendarDays,
  Users,
  Ticket,
  Mail,
  Tags,
  MapPin,
  Briefcase,
  TrendingUp,
  Settings,
  Menu,
  X,
  PlusCircle,
  LayoutDashboard,
  LayoutGrid,
  Send,
  BarChart3,
  Box,
  CreditCard,
  ShoppingCart,
  ChevronRight,
  ChevronDown,
  LogOut,
  User,
  Bell,
  HelpCircle,
  Home,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  DollarSign,
  Wallet,
  Key,
  Star,
  Share,
  Share2,
  Calendar,
  Link as LinkIcon,
  Shuffle,
  UserPlus,
  History,
  Tag,
  Store,
  WifiOff,
  Bluetooth,
  Percent,
  Hotel,
  Utensils,
  Award,
  Megaphone,
  // ReconcilePro-style icons
  Grid3x3,
  Receipt,
  ArrowLeftRight,
  BarChart2,
  Cog,
} from "lucide-react";
import { useCurrentRole } from "@/hooks/use-current-role";

import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

type Role = "ADMIN" | "USER" | "ORGANIZER" | "VENDOR" | "SUPERADMIN" | "DEVELOPER" | "PARTNER";

interface NavItem {
  href: string;
  label: string;
  icon: React.ReactNode;
  badge?: string | React.ReactNode;
  badgeColor?: string;
  children?: NavItem[];
}

type NavItems = {
  [K in Role]: NavItem[];
};

export function Sidebar() {
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const role = useCurrentRole() as Role;

  const toggleSidebar = () => setIsOpen(!isOpen);

  const navItems: NavItems = {
    USER: [
      { href: "/dashboard/user", label: "Dashboard", icon: <Grid3x3 size={18} /> },
      { href: "/dashboard/user/browse-events", label: "Browse Events", icon: <CalendarDays size={18} />,  },
      { href: "/partners", label: "Partners", icon: <Hotel size={18} /> },
      { href: "/dashboard/user/orders", label: "My Orders", icon: <Receipt size={18} /> },
      { href: "/my-tickets", label: "My Tickets", icon: <Ticket size={18} /> },
      {
        href: "/nfc-store",
        label: "NFC Devices",
        icon: <CreditCard size={18} />,
        children: [
          { href: "/nfc-store", label: "Buy NFC Devices", icon: <ShoppingCart size={16} /> },
          { href: "/nfc-topup", label: "Top Up Balance", icon: <Wallet size={16} /> },
          { href: "/my-tickets?tab=nfc", label: "Manage Devices", icon: <Cog size={16} /> },
        ]
      },
      { href: "/dashboard/wallet", label: "Wallet", icon: <Wallet size={18} />,  },
      { href: "/dashboard/user/profile", label: "Profile", icon: <User size={18} /> },
      { href: "/dashboard/user/settings", label: "Settings", icon: <Cog size={18} /> },
    ],
    ADMIN: [
      { href: "/dashboard/admin", label: "Dashboard", icon: <LayoutDashboard size={18} /> },
      {
        href: "/dashboard/admin/users",
        label: "User Management",
        icon: <Users size={18} />,
        children: [
          { href: "/dashboard/admin/users", label: "All Users", icon: <Users size={16} /> },
          { href: "/dashboard/admin/organizers", label: "Organizers", icon: <Briefcase size={16} /> },
        ]
      },
      {
        href: "/dashboard/admin/verifications",
        label: "Verifications",
        icon: <CheckCircle size={18} />,
        children: [
          { href: "/dashboard/admin/verifications/organizers", label: "Organizers", icon: <Briefcase size={16} /> },
          { href: "/dashboard/admin/verifications/partners", label: "Partners", icon: <Hotel size={16} /> },
          { href: "/dashboard/admin/verifications/vendors", label: "Vendors", icon: <Store size={16} /> },
        ]
      },
      {
        href: "/admin/finance",
        label: "Finance & Teams",
        icon: <DollarSign size={18} />,
        children: [
          { href: "/admin/finance/dashboard", label: "Finance Overview", icon: <BarChart3 size={16} /> },
          { href: "/admin/finance/transactions", label: "Transactions", icon: <ArrowLeftRight size={16} /> },
          { href: "/admin/finance/withdrawals", label: "Withdrawals", icon: <Wallet size={16} /> },
          { href: "/admin/finance/fees", label: "Fee Configuration", icon: <Percent size={16} /> },
          { href: "/dashboard/admin/teams", label: "Teams", icon: <Users size={16} /> },
        ]
      },
      {
        href: "/admin/nfc",
        label: "NFC System",
        icon: <CreditCard size={18} />,
        children: [
          { href: "/admin/nfc/dashboard", label: "Overview", icon: <Grid3x3 size={16} /> },
          { href: "/admin/nfc/transactions", label: "Transactions", icon: <ArrowLeftRight size={16} /> },
          { href: "/admin/nfc/vendors", label: "Vendors", icon: <Store size={16} /> },
          { href: "/admin/nfc/cards", label: "Card Inventory", icon: <CreditCard size={16} /> },
          { href: "/dashboard/admin/nfc-pricing", label: "Pricing", icon: <DollarSign size={16} /> },
          { href: "/admin/nfc/analytics", label: "Analytics", icon: <BarChart2 size={16} /> },
        ]
      },
      {
        href: "/admin/events",
        label: "Events & Marketing",
        icon: <CalendarDays size={18} />,
        children: [
          { href: "/admin/events", label: "All Events", icon: <CalendarDays size={16} /> },
          { href: "/admin/events/moderate", label: "Moderate Events", icon: <AlertTriangle size={16} /> },
          { href: "/admin/featuring", label: "Featured Events", icon: <Star size={16} /> },
          { href: "/admin/featuring/ab-testing", label: "A/B Testing", icon: <Shuffle size={16} /> },
        ]
      },
      {
        href: "/dashboard/admin/partners",
        label: "Partners",
        icon: <Hotel size={18} />,
        children: [
          { href: "/dashboard/admin/partners", label: "All Partners", icon: <Users size={16} /> },
          { href: "/admin/partners/featured", label: "Featured Partners", icon: <Star size={16} /> },
          { href: "/dashboard/admin/partners/promotions", label: "Promotions", icon: <Tag size={16} /> },
        ]
      },
      {
        href: "/dashboard/admin/vendors",
        label: "Vendors",
        icon: <Store size={18} />,
        children: [
          { href: "/dashboard/admin/vendors", label: "All Vendors", icon: <Store size={16} /> },
          { href: "/admin/vendors/featured", label: "Featured Vendors", icon: <Star size={16} /> },
        ]
      },
      { href: "/wallet", label: "Wallet", icon: <Wallet size={18} /> },
      { href: "/admin/settings", label: "Settings", icon: <Settings size={18} /> },
    ],
    ORGANIZER: [
      { href: "/dashboard/organizer", label: "Dashboard", icon: <Grid3x3 size={18} /> },
      {
        href: "/dashboard/organizer/events",
        label: "Events",
        icon: <CalendarDays size={18} />,
        children: [
          { href: "/dashboard/organizer/events/create", label: "Create Event", icon: <PlusCircle size={16} /> },
          { href: "/dashboard/organizer/events/myEvents", label: "My Events", icon: <CalendarDays size={16} /> },
          { href: "/dashboard/organizer/events/featured", label: "Featured Events", icon: <Star size={16} /> },
          { href: "/dashboard/organizer/events/seating", label: "Seating", icon: <LayoutGrid size={16} /> },
        ]
      },
      {
        href: "/dashboard/organizer/orders",
        label: "Orders & Tickets",
        icon: <Ticket size={18} />,
        badge: <OrderTicketCounts type="orders" />,
        children: [
          { href: "/dashboard/organizer/orders/active", label: "Active Orders", icon: <Clock size={16} />, badge: <OrderTicketCounts type="activeOrders" /> },
          { href: "/dashboard/organizer/orders/completed", label: "Completed", icon: <CheckCircle size={16} />, badge: <OrderTicketCounts type="completedOrders" /> },
          { href: "/dashboard/organizer/orders/cancelled", label: "Cancelled", icon: <AlertCircle size={16} />, badge: <OrderTicketCounts type="cancelledOrders" /> },
        ]
      },
      { href: "/dashboard/organizer/attendees", label: "Attendees", icon: <Users size={18} /> },
      {
        href: "/dashboard/organizer/team",
        label: "Team",
        icon: <Users size={18} />,
        children: [
          { href: "/dashboard/organizer/team", label: "My Teams", icon: <Users size={16} /> },
          { href: "/dashboard/organizer/team/invitations", label: "Invitations", icon: <Mail size={16} /> },
        ]
      },
      {
        href: "/organizer/partners",
        label: "Partners",
        icon: <Hotel size={18} />,
        children: [
          { href: "/organizer/partners", label: "All Partners", icon: <Hotel size={16} /> },
          { href: "/organizer/partners/add", label: "Add Partner", icon: <PlusCircle size={16} /> },
          { href: "/organizer/partners/promotions", label: "Promotions", icon: <Tag size={16} /> },
        ]
      },
      {
        href: "/dashboard/organizer/finance",
        label: "Finance",
        icon: <DollarSign size={18} />,
        children: [
          { href: "/dashboard/organizer/finance", label: "Overview", icon: <BarChart2 size={16} /> },
          { href: "/dashboard/organizer/finance/transactions", label: "Transactions", icon: <CreditCard size={16} /> },
          { href: "/dashboard/wallet", label: "Wallet", icon: <Wallet size={16} /> },
        ]
      },
      {
        href: "/dashboard/organizer/nfc",
        label: "NFC Payments",
        icon: <CreditCard size={18} />,
        children: [
          { href: "/dashboard/organizer/nfc/dashboard", label: "Dashboard", icon: <LayoutDashboard size={16} /> },
          { href: "/dashboard/organizer/nfc/cards", label: "Card Management", icon: <CreditCard size={16} /> },
          { href: "/dashboard/organizer/nfc/transactions", label: "Transactions", icon: <History size={16} /> },
          { href: "/dashboard/organizer/nfc/analytics", label: "Analytics", icon: <BarChart3 size={16} /> },
        ]
      },
      {
        href: "/dashboard/organizer/marketing",
        label: "Marketing",
        icon: <Megaphone size={18} />,
        children: [
          { href: "/dashboard/organizer/marketing/campaigns", label: "Campaigns", icon: <Send size={16} /> },
          { href: "/dashboard/organizer/marketing/email-templates", label: "Email Templates", icon: <FileText size={16} /> },
          { href: "/dashboard/organizer/email", label: "Email Inbox", icon: <Mail size={16} /> },
          { href: "/dashboard/featured", label: "Promote Events", icon: <Star size={16} /> },
        ]
      },
      { href: "/dashboard/organizer/venues", label: "Venues", icon: <MapPin size={18} /> },
      { href: "/dashboard/organizer/analytics", label: "Analytics", icon: <BarChart2 size={18} /> },
      {
        href: "/dashboard/organizer/integrations",
        label: "Integrations",
        icon: <Share size={18} />,
        children: [
          { href: "/dashboard/organizer/integrations/webhooks", label: "Webhooks", icon: <LinkIcon size={16} /> },
          { href: "/dashboard/organizer/integrations/payment", label: "Payment Gateways", icon: <CreditCard size={16} /> },
          { href: "/dashboard/organizer/integrations/calendar", label: "Calendar Sync", icon: <Calendar size={16} /> },
          { href: "/dashboard/developer", label: "Developer Portal", icon: <Key size={16} />, badge: "API", badgeColor: "bg-purple-500" },
        ]
      },
      { href: "/dashboard/organizer/profile", label: "Profile", icon: <User size={18} /> },
      { href: "/dashboard/organizer/settings", label: "Settings", icon: <Settings size={18} /> },
    ],
    VENDOR: [
      { href: "/dashboard/vendor", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      {
        href: "/dashboard/vendor/events",
        label: "Events",
        icon: <CalendarDays size={20} />,
        children: [
          { href: "/dashboard/vendor/events", label: "My Events", icon: <CalendarDays size={20} /> },
          { href: "/dashboard/vendor/events/apply", label: "Apply for Events", icon: <FileText size={20} /> },
          { href: "/dashboard/vendor/events/applications", label: "My Applications", icon: <Store size={20} />, },
        ]
      },
      {
        href: "/dashboard/vendor/devices",
        label: "Devices & Scanning",
        icon: <Bluetooth size={20} />,
        children: [
          { href: "/dashboard/vendor/devices", label: "Device Hub", icon: <Bluetooth size={20} /> },
          { href: "/dashboard/vendor/scanner", label: "Device Scanner", icon: <Bluetooth size={20} /> },
          { href: "/dashboard/vendor/nfc/terminal", label: "NFC Terminal", icon: <CreditCard size={20} /> },
        ]
      },
      {
        href: "/dashboard/vendor/nfc",
        label: "NFC Payments",
        icon: <CreditCard size={20} />,
        children: [
          { href: "/dashboard/vendor/enhanced-pos", label: "Enhanced POS", icon: <ShoppingCart size={20} /> },
          { href: "/dashboard/vendor/nfc/transactions", label: "Transaction History", icon: <History size={20} /> },
          { href: "/dashboard/vendor/nfc/analytics", label: "Sales Analytics", icon: <BarChart3 size={20} /> },
          { href: "/dashboard/vendor/nfc/receipts", label: "Digital Receipts", icon: <FileText size={20} /> },
          { href: "/dashboard/vendor/nfc/cards", label: "Card Management", icon: <CreditCard size={20} /> },
          { href: "/dashboard/vendor/nfc/offline", label: "Offline Mode", icon: <WifiOff size={20} /> },
          { href: "/dashboard/vendor/nfc/settings", label: "Terminal Settings", icon: <Settings size={20} /> }
        ]
      },
      {
        href: "/vendor/products",
        label: "Products",
        icon: <Box size={20} />,
        children: [
          { href: "/vendor/products", label: "All Products", icon: <Box size={20} /> },
          { href: "/vendor/createproduct", label: "Create Product", icon: <PlusCircle size={20} /> },
          { href: "/vendor/products/categories", label: "Categories", icon: <Tag size={20} /> },
        ]
      },
      { href: "/dashboard/vendor/orders", label: "Orders", icon: <ShoppingCart size={20} /> },
      { href: "/dashboard/vendor/analytics", label: "Analytics", icon: <BarChart3 size={20} /> },
      {
        href: "/dashboard/vendor/finance",
        label: "Finance",
        icon: <DollarSign size={20} />,
        children: [
          { href: "/dashboard/vendor/finance", label: "Overview", icon: <LayoutDashboard size={20} /> },
          { href: "/vendor/finance/reports", label: "Sales Reports", icon: <BarChart3 size={20} /> },
          { href: "/dashboard/vendor/finance/payments", label: "Payments", icon: <CreditCard size={20} /> },
          { href: "/dashboard/vendor/finance/withdrawals", label: "Withdrawals", icon: <Wallet size={20} /> },
          { href: "/dashboard/vendor/pos-rentals", label: "POS Rentals", icon: <CreditCard size={20} /> },
        ]
      },
      {
        href: "/dashboard/vendor/featuring",
        label: "Feature Business",
        icon: <Star size={20} />,
        badge: "Boost Visibility",
        badgeColor: "bg-gradient-to-r from-yellow-400 to-orange-400"
      },
      { href: "/dashboard/vendor/verification", label: "Verification", icon: <CheckCircle size={20} /> },
      { href: "/dashboard/vendor/staff", label: "Staff", icon: <Users size={20} /> },
      { href: "/dashboard/vendor/settings", label: "Settings", icon: <Settings size={20} /> },
    ],
    SUPERADMIN: [
      { href: "/dashboard/superadmin", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      { href: "/dashboard/superadmin/users", label: "Users", icon: <Users size={20} /> },
      { href: "/dashboard/superadmin/vendors", label: "Vendors", icon: <Briefcase size={20} /> },
      { href: "/dashboard/superadmin/events", label: "Events", icon: <CalendarDays size={20} /> },
      { href: "/dashboard/superadmin/analytics", label: "Analytics", icon: <BarChart3 size={20} /> },
    ],

    DEVELOPER: [
      { href: "/dashboard/developer", label: "Dashboard", icon: <LayoutDashboard size={20} /> },
      { href: "/dashboard/developer/api-keys", label: "API Keys", icon: <Key size={20} /> },
      { href: "/dashboard/developer/docs", label: "API Documentation", icon: <FileText size={20} />,  },
      { href: "/dashboard/developer/analytics", label: "API Analytics", icon: <BarChart3 size={20} /> },
      { href: "/dashboard/developer/alerts", label: "API Alerts", icon: <AlertTriangle size={20} /> },
    ],
    PARTNER: [
      { href: "/dashboard/partner", label: "Partner Dashboard", icon: <Hotel size={20} /> },
      { href: "/dashboard/partner/profile", label: "My Profile", icon: <User size={20} /> },
      { href: "/dashboard/partner/settings", label: "Settings", icon: <Settings size={20} /> },
      // Add other partner-specific links here, e.g., for menu, promotions, loyalty
      { href: "/dashboard/partner/menu", label: "Manage Menu", icon: <Utensils size={20} /> },
      { href: "/dashboard/partner/promotions", label: "Promotions", icon: <Tag size={20} /> },
      { href: "/dashboard/partner/loyalty", label: "Loyalty Program", icon: <Award size={20} /> },
      { href: "/dashboard/partner/events", label: "Event Partnerships", icon: <Calendar size={20} /> },
      { href: "/dashboard/partner/transactions", label: "Transactions", icon: <CreditCard size={20} /> },
      { href: "/dashboard/partner/reviews", label: "Reviews", icon: <Star size={20} /> },
      { href: "/dashboard/partner/analytics", label: "Analytics", icon: <BarChart3 size={20} /> },
    ],
  };

  const currentNavItems = navItems[role] || [];

  // Track expanded menu items
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    'Events': true,
    'Marketing': true,
    'Team Management': true,
    'Finance': true,
    'NFC Payments': true,
    'Devices & Scanning': true,
    'Products': true,
    'Integrations': true,
    'Partners': true
  });

  // Toggle expanded state for an item
  const toggleExpand = (label: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  // Check if a nav item should be shown as active
  const isNavItemActive = (item: NavItem): boolean => {
    if (pathname === item.href) return true;
    if (item.children) {
      return item.children.some(child => pathname === child.href);
    }
    return false;
  };

  // Render a nav item (supports nested items)
  const renderNavItem = (item: NavItem) => {
    const isActive = isNavItemActive(item);
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems[item.label];

    if (hasChildren) {
      return (
        <div key={item.href} className="mb-1">
          <button
            onClick={() => toggleExpand(item.label)}
            className={cn(
              "w-full flex items-center justify-between py-2.5 px-3 rounded-lg transition-all duration-200 text-sm font-medium group",
              isActive
                ? "bg-emerald-50 text-emerald-700 shadow-sm"
                : "text-gray-700 hover:bg-gray-50"
            )}
          >
            <div className="flex items-center gap-3 min-w-0 overflow-hidden">
              <div className={cn(
                "flex-shrink-0 transition-colors duration-200 w-5 h-5 flex items-center justify-center",
                isActive ? "text-emerald-600" : "text-gray-500 group-hover:text-gray-700"
              )}>
                {item.icon}
              </div>
              <span className="whitespace-nowrap overflow-hidden text-ellipsis font-medium">{item.label}</span>
              {item.badge && (
                typeof item.badge === 'string' ? (
                  <Badge className={cn(
                    'ml-2 text-xs flex-shrink-0',
                    isActive ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-600'
                  )}>
                    {item.badge}
                  </Badge>
                ) : (
                  <div className="flex-shrink-0">{item.badge}</div>
                )
              )}
            </div>
            <ChevronDown
              size={14}
              className={cn(
                "transition-all duration-200 flex-shrink-0 ml-2",
                isExpanded ? "transform rotate-180" : "",
                isActive ? "text-emerald-600" : "text-gray-400"
              )}
            />
          </button>

          {isExpanded && (
            <div className="ml-4 mt-1 space-y-1 border-l border-gray-100 pl-3">
              {item.children?.map(child => (
                <Link
                  key={child.href}
                  href={child.href}
                  className={cn(
                    "flex items-center justify-between py-2 px-3 rounded-md transition-all duration-200 text-sm group",
                    pathname === child.href
                      ? "bg-emerald-50 text-emerald-700 shadow-sm border-l-2 border-emerald-500"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  )}
                >
                  <div className="flex items-center min-w-0 overflow-hidden">
                    <div className={cn(
                      "flex-shrink-0 mr-3 transition-colors duration-200 w-4 h-4 flex items-center justify-center",
                      pathname === child.href ? "text-emerald-600" : "text-gray-400 group-hover:text-gray-600"
                    )}>
                      {child.icon}
                    </div>
                    <span className="whitespace-nowrap overflow-hidden text-ellipsis">{child.label}</span>
                  </div>
                  {child.badge && (
                    typeof child.badge === 'string' ? (
                      <Badge className={cn(
                        'text-xs flex-shrink-0 ml-2',
                        pathname === child.href ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-600'
                      )}>
                        {child.badge}
                      </Badge>
                    ) : (
                      <div className="flex-shrink-0">{child.badge}</div>
                    )
                  )}
                </Link>
              ))}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          "flex items-center justify-between py-2.5 px-3 rounded-lg transition-all duration-200 text-sm font-medium group",
          isActive
            ? "bg-emerald-50 text-emerald-700 shadow-sm"
            : "text-gray-700 hover:bg-gray-50"
        )}
      >
        <div className="flex items-center gap-3 min-w-0 overflow-hidden">
          <div className={cn(
            "flex-shrink-0 transition-colors duration-200 w-5 h-5 flex items-center justify-center",
            isActive ? "text-emerald-600" : "text-gray-500 group-hover:text-gray-700"
          )}>
            {item.icon}
          </div>
          <span className="whitespace-nowrap overflow-hidden text-ellipsis font-medium">{item.label}</span>
        </div>
        {item.badge && (
          typeof item.badge === 'string' ? (
            <Badge className={cn(
              'text-xs flex-shrink-0 ml-2',
              isActive ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-600'
            )}>
              {item.badge}
            </Badge>
          ) : (
            <div className="flex-shrink-0">{item.badge}</div>
          )
        )}
      </Link>
    );
  };



  return (
    <div className="h-full bg-white flex flex-col shadow-lg border-r border-gray-100">
      {/* Header Section */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex justify-center">
          <div className="w-10 h-10 bg-emerald-500 rounded-xl flex items-center justify-center">
            <Image src={logo} alt="Logo" width={24} height={24} className="rounded-md" />
          </div>
        </div>
      </div>

      {/* Navigation Section */}
      <div className="flex-1 overflow-y-auto px-3 py-4 custom-scrollbar">
        {/* Core Navigation */}
        <div className="mb-6">
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">
            Core
          </div>
          <nav className="space-y-1">
            {currentNavItems.slice(0, role === 'ORGANIZER' ? 4 : 2).map(item => renderNavItem(item))}
          </nav>
        </div>

        {/* Management Section */}
        <div className="mb-6">
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">
            {role === 'ORGANIZER' ? 'Business' : 'Management'}
          </div>
          <nav className="space-y-1">
            {currentNavItems.slice(role === 'ORGANIZER' ? 4 : 2, -2).map(item => renderNavItem(item))}
          </nav>
        </div>

        {/* System Section */}
        <div className="mb-6">
          <div className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">
            System
          </div>
          <nav className="space-y-1">
            {currentNavItems.slice(-2).map(item => renderNavItem(item))}
          </nav>
        </div>
      </div>

      {/* Bottom Section - Sign Out */}
      <div className="p-4 border-t border-gray-100">
        <button
          onClick={() => {
            window.location.href = '/api/auth/direct-signout';
          }}
          className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-700 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 group"
        >
          <LogOut size={16} className="min-w-[16px] mr-3 text-gray-500 group-hover:text-red-500 transition-colors duration-200" />
          <span className="whitespace-nowrap overflow-hidden text-ellipsis">Sign Out</span>
        </button>
      </div>
    </div>
  );
}
export default Sidebar;