@import "react-day-picker/dist/style.css";
@import "../styles/carousel.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInMenu {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes loading-bar {
  0% { width: 0%; }
  20% { width: 20%; }
  40% { width: 40%; }
  60% { width: 60%; }
  80% { width: 80%; }
  95% { width: 95%; }
  100% { width: 100%; }
}

@keyframes shimmer {
  0% { background-position: -1000px 0; }
  100% { background-position: 1000px 0; }
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out forwards;
}

.animate-slideInMenu {
  animation: slideInMenu 0.3s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-loading-bar {
  animation: loading-bar 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0) 100%);
  background-size: 1000px 100%;
  animation: shimmer 2s infinite linear;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-300;
  }

  /* Force dark mode styles */
  .dark {
    color-scheme: dark;
  }

  /* Improved text contrast for light mode */
  h1 {
    @apply text-gray-900 font-bold;
  }

  h2, h3 {
    @apply text-gray-800 font-semibold;
  }

  p, span, div {
    @apply text-gray-700;
  }

  label {
    @apply text-gray-700 font-medium;
  }

  /* Improved text contrast for dark mode */
  .dark h1 {
    @apply text-gray-50 font-bold;
  }

  .dark h2, .dark h3 {
    @apply text-gray-100 font-semibold;
  }

  .dark p, .dark span, .dark div {
    @apply text-gray-300;
  }

  .dark label {
    @apply text-gray-200 font-medium;
  }
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.3);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.5);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.3) transparent;
}

/* Input field styles - Light Mode */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
input[type="date"],
input[type="time"],
input[type="tel"],
input[type="url"],
input[type="search"],
textarea,
select {
  background-color: white !important;
  color: #333 !important;
  border-color: #d1d5db !important;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s !important;
}

/* Input field styles - Dark Mode */
.dark input[type="text"],
.dark input[type="number"],
.dark input[type="email"],
.dark input[type="password"],
.dark input[type="date"],
.dark input[type="time"],
.dark input[type="tel"],
.dark input[type="url"],
.dark input[type="search"],
.dark textarea,
.dark select {
  background-color: #1f2937 !important;
  color: #e5e7eb !important;
  border-color: #4b5563 !important;
}

/* Placeholder styles - Light Mode */
input::placeholder,
textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1;
}

/* Placeholder styles - Dark Mode */
.dark input::placeholder,
.dark textarea::placeholder {
  color: #6b7280 !important;
  opacity: 1;
}

/* Button styles - Light Mode */
button,
.btn,
[type="button"],
[type="submit"] {
  @apply transition-all duration-200;
}

/* Button hover effects - Light Mode */
button:not([disabled]):hover,
.btn:not([disabled]):hover,
[type="button"]:not([disabled]):hover,
[type="submit"]:not([disabled]):hover {
  @apply shadow-md transform scale-[1.02];
}

/* Button styles - Dark Mode */
.dark button:not(.bg-white):not(.bg-gray-100):not(.bg-blue-500):not(.bg-red-500):not(.bg-green-500),
.dark .btn:not(.bg-white):not(.bg-gray-100):not(.bg-blue-500):not(.bg-red-500):not(.bg-green-500),
.dark [type="button"]:not(.bg-white):not(.bg-gray-100):not(.bg-blue-500):not(.bg-red-500):not(.bg-green-500),
.dark [type="submit"]:not(.bg-white):not(.bg-gray-100):not(.bg-blue-500):not(.bg-red-500):not(.bg-green-500) {
  @apply bg-gray-800 text-white border-gray-700;
}

/* Card styles - Light Mode */
.card,
.shadow-card {
  @apply bg-white border border-gray-200 shadow-sm transition-shadow duration-200;
}

/* Card styles - Dark Mode */
.dark .card,
.dark .shadow-card {
  @apply bg-gray-800 border-gray-700 shadow-md;
}

/* Focus styles for better accessibility */
button:focus,
a:focus,
input:focus,
select:focus,
textarea:focus {
  @apply outline-none ring-2 ring-blue-500 ring-opacity-50;
}

/* Table styles - Light Mode */
table {
  @apply w-full border-collapse bg-white;
}

table th {
  @apply bg-gray-50 text-left text-gray-700 font-semibold p-3 border-b border-gray-200;
}

table td {
  @apply p-3 border-b border-gray-200 text-gray-700;
}

table tr:hover td {
  @apply bg-gray-50;
}

/* Table styles - Dark Mode */
.dark table {
  @apply bg-gray-800;
}

.dark table th {
  @apply bg-gray-900 text-gray-200 border-gray-700;
}

.dark table td {
  @apply border-gray-700 text-gray-300;
}

.dark table tr:hover td {
  @apply bg-gray-700;
}

/* Dashboard card styles - Light Mode */
.dashboard-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 transition-all duration-200;
}

.dashboard-card:hover {
  @apply shadow-md;
}

/* Dashboard card styles - Dark Mode */
.dark .dashboard-card {
  @apply bg-gray-800 border-gray-700 shadow-md;
}

.dark .dashboard-card:hover {
  @apply shadow-lg;
}

/* Modal styles - Light Mode */
.modal-content {
  @apply bg-white rounded-lg shadow-lg border border-gray-200 p-6 max-w-md mx-auto;
}

.modal-header {
  @apply text-xl font-semibold text-gray-900 mb-4;
}

.modal-body {
  @apply text-gray-700 mb-6;
}

.modal-footer {
  @apply flex justify-end space-x-3 pt-4 border-t border-gray-200;
}

/* Modal styles - Dark Mode */
.dark .modal-content {
  @apply bg-gray-800 border-gray-700;
}

.dark .modal-header {
  @apply text-gray-100;
}

.dark .modal-body {
  @apply text-gray-300;
}

.dark .modal-footer {
  @apply border-gray-700;
}

/* Dialog backdrop - Light Mode */
.dialog-backdrop {
  @apply fixed inset-0 bg-black bg-opacity-25 backdrop-blur-sm flex items-center justify-center z-50;
}

/* Dialog backdrop - Dark Mode */
.dark .dialog-backdrop {
  @apply bg-black bg-opacity-50;
}

/* Navigation styles - Light Mode */
.nav-link {
  @apply px-4 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200;
}

.nav-link.active {
  @apply bg-blue-50 text-blue-700 font-medium;
}

/* Navigation styles - Dark Mode */
.dark .nav-link {
  @apply text-gray-300 hover:text-white hover:bg-gray-700;
}

.dark .nav-link.active {
  @apply bg-gray-800 text-blue-400 font-medium;
}

/* Sidebar styles - Light Mode */
.sidebar {
  @apply bg-white border-r border-gray-200 h-full transition-all duration-300;
}

.sidebar-item {
  @apply flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-md transition-colors duration-200;
}

.sidebar-item.active {
  @apply bg-blue-50 text-blue-700 font-medium;
}

/* Sidebar styles - Dark Mode */
.dark .sidebar {
  @apply bg-gray-900 border-gray-700;
}

.dark .sidebar-item {
  @apply text-gray-300 hover:bg-gray-800 hover:text-white;
}

.dark .sidebar-item.active {
  @apply bg-gray-800 text-blue-400;
}

/* Form styles - Light Mode */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
}

.form-error {
  @apply mt-1 text-sm text-red-500;
}

.form-hint {
  @apply mt-1 text-xs text-gray-500;
}

/* Form styles - Dark Mode */
.dark .form-label {
  @apply text-gray-200;
}

.dark .form-input {
  @apply border-gray-600 bg-gray-800 text-white;
}

.dark .form-error {
  @apply text-red-400;
}

.dark .form-hint {
  @apply text-gray-400;
}

/* Checkbox and Radio styles - Light Mode */
.checkbox-label,
.radio-label {
  @apply flex items-center cursor-pointer;
}

.checkbox-input,
.radio-input {
  @apply mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500;
}

/* Checkbox and Radio styles - Dark Mode */
.dark .checkbox-input,
.dark .radio-input {
  @apply border-gray-600 bg-gray-700;
}