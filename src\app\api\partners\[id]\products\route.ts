import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/partners/:id/products
 * Get products/services for a specific partner
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const partnerId = resolvedParams.id;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type'); // PRODUCT, SERVICE, PACKAGE, EXPERIENCE
    const category = searchParams.get('category');
    const featured = searchParams.get('featured') === 'true';
    const popular = searchParams.get('popular') === 'true';
    const status = searchParams.get('status') || 'ACTIVE';
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build where clause
    const where: any = {
      partnerId,
      status: status === 'all' ? undefined : status,
      isAvailable: true,
    };

    if (type && type !== 'all') {
      where.type = type;
    }

    if (category && category !== 'all') {
      where.category = category;
    }

    if (featured) {
      where.isFeatured = true;
    }

    if (popular) {
      where.isPopular = true;
    }

    // Remove undefined values
    Object.keys(where).forEach(key => where[key] === undefined && delete where[key]);

    // Build orderBy clause
    const orderBy: any = {};
    if (sortBy === 'price') {
      orderBy.price = sortOrder;
    } else if (sortBy === 'rating') {
      orderBy.rating = sortOrder;
    } else if (sortBy === 'popularity') {
      orderBy.totalSales = sortOrder;
    } else if (sortBy === 'name') {
      orderBy.name = sortOrder;
    } else {
      orderBy.createdAt = sortOrder;
    }

    // Get products with pagination
    const [products, totalCount] = await Promise.all([
      db.partnerProduct.findMany({
        where,
        orderBy,
        take: limit,
        skip: offset,
        include: {
          partner: {
            select: {
              id: true,
              businessName: true,
              logo: true,
              rating: true,
              totalReviews: true,
            },
          },
          reviews: {
            where: { isPublished: true },
            select: {
              id: true,
              rating: true,
              title: true,
              comment: true,
              createdAt: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: 3,
          },
        },
      }),
      db.partnerProduct.count({ where }),
    ]);

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const currentPage = Math.floor(offset / limit) + 1;
    const hasNextPage = currentPage < totalPages;
    const hasPrevPage = currentPage > 1;

    return NextResponse.json({
      products,
      pagination: {
        totalCount,
        totalPages,
        currentPage,
        hasNextPage,
        hasPrevPage,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error fetching partner products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch partner products' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/partners/:id/products
 * Create a new product/service for a partner
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();
    const resolvedParams = await params;
    const partnerId = resolvedParams.id;

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user owns this partner profile or is admin
    const partner = await db.partner.findUnique({
      where: { id: partnerId },
      select: { userId: true },
    });

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }

    if (partner.userId !== user.id && user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      description,
      shortDescription,
      type,
      category,
      subcategory,
      price,
      originalPrice,
      currency,
      imageUrl,
      galleryImages,
      status,
      isAvailable,
      isFeatured,
      isPopular,
      stockQuantity,
      minOrderQuantity,
      maxOrderQuantity,
      duration,
      durationUnit,
      location,
      inclusions,
      exclusions,
      requirements,
      tags,
      seoTitle,
      seoDescription,
      seoKeywords,
    } = body;

    // Validate required fields
    if (!name || !type || !category || price === undefined) {
      return NextResponse.json(
        { error: 'Missing required fields: name, type, category, price' },
        { status: 400 }
      );
    }

    // Create the product
    const product = await db.partnerProduct.create({
      data: {
        partnerId,
        name,
        description,
        shortDescription,
        type,
        category,
        subcategory,
        price: parseFloat(price),
        originalPrice: originalPrice ? parseFloat(originalPrice) : null,
        currency: currency || 'ZMW',
        imageUrl,
        galleryImages: galleryImages || null,
        status: status || 'DRAFT',
        isAvailable: isAvailable !== undefined ? isAvailable : true,
        isFeatured: isFeatured || false,
        isPopular: isPopular || false,
        stockQuantity: stockQuantity ? parseInt(stockQuantity) : null,
        minOrderQuantity: minOrderQuantity ? parseInt(minOrderQuantity) : 1,
        maxOrderQuantity: maxOrderQuantity ? parseInt(maxOrderQuantity) : null,
        duration: duration ? parseInt(duration) : null,
        durationUnit,
        location,
        inclusions: inclusions || [],
        exclusions: exclusions || [],
        requirements: requirements || [],
        tags: tags || [],
        seoTitle,
        seoDescription,
        seoKeywords: seoKeywords || [],
      },
      include: {
        partner: {
          select: {
            id: true,
            businessName: true,
            logo: true,
          },
        },
      },
    });

    return NextResponse.json(product, { status: 201 });
  } catch (error) {
    console.error('Error creating partner product:', error);
    return NextResponse.json(
      { error: 'Failed to create partner product' },
      { status: 500 }
    );
  }
}
