import { NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';

/**
 * GET /api/vendor/profile/details
 * Get detailed vendor profile information for the authenticated vendor
 */
export async function GET() {
  try {
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get vendor profile from database
    const vendorProfile = await db.vendorProfile.findUnique({
      where: {
        userId: user.id!,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
            role: true,
          },
        },
      },
    });

    if (!vendorProfile) {
      return NextResponse.json({ error: 'Vendor profile not found' }, { status: 404 });
    }

    // Format the response
    const response = {
      id: vendorProfile.id,
      userId: vendorProfile.userId,
      businessName: vendorProfile.businessName,
      businessType: vendorProfile.businessType,
      registrationNumber: vendorProfile.registrationNumber,
      taxPayerIdNumber: vendorProfile.taxPayerIdNumber,
      yearEstablished: vendorProfile.yearEstablished,
      description: vendorProfile.description,
      productCategories: vendorProfile.productCategories,
      serviceCategories: vendorProfile.serviceCategories,
      specializations: vendorProfile.specializations,
      certifications: vendorProfile.certifications,
      logo: vendorProfile.logo,
      bannerImage: vendorProfile.bannerImage,
      galleryImages: vendorProfile.galleryImages,
      email: vendorProfile.email,
      phoneNumber: vendorProfile.phoneNumber,
      alternativePhoneNumber: vendorProfile.alternativePhoneNumber,
      website: vendorProfile.website,
      socialLinks: vendorProfile.socialLinks,
      physicalAddress: vendorProfile.physicalAddress,
      city: vendorProfile.city,
      province: vendorProfile.province,
      postalCode: vendorProfile.postalCode,
      country: vendorProfile.country,
      acceptedPaymentMethods: vendorProfile.acceptedPaymentMethods,
      employeeCount: vendorProfile.employeeCount,
      businessSize: vendorProfile.businessSize,
      businessHours: vendorProfile.businessHours,
      verificationStatus: vendorProfile.verificationStatus,
      verifiedAt: vendorProfile.verifiedAt,
      featured: vendorProfile.featured,
      averageRating: vendorProfile.averageRating,
      totalReviews: vendorProfile.totalReviews,
      totalSales: vendorProfile.totalSales,
      totalOrders: vendorProfile.totalOrders,
      createdAt: vendorProfile.createdAt,
      updatedAt: vendorProfile.updatedAt,
      user: vendorProfile.user,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching vendor profile details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch vendor profile details' },
      { status: 500 }
    );
  }
}
