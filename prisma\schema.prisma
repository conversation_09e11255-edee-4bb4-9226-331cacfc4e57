generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider          = "postgresql"
  url               = env("DATABASE_URL")
  directUrl         = env("DIRECT_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
}

model User {
  id                     String                       @id @default(cuid())
  name                   String?
  email                  String?                      @unique
  emailVerified          DateTime?                    @map("email_verified")
  image                  String?
  password               String?
  isTwoFactorEnabled     Boolean                      @default(false)
  role                   UserRole                     @default(USER)
  accessToken            String?                      @unique
  createdAt              DateTime                     @default(now())
  accountBalance         Float                        @default(0)
  isNewCustomer          Boolean                      @default(true)
  lastPurchaseDate       DateTime?
  managedById            String?
  subscriptionEndDate    DateTime?
  subscriptionStartDate  DateTime?
  subscriptionTier       OrganizerSubscriptionTier    @default(NONE)
  updatedAt              DateTime                     @updatedAt
  apiKeys                ApiKey[]
  bankAccounts           BankAccount[]
  comments               Comment[]
  emails                 Email[]
  events                 Event[]
  eventPayouts           EventPayout[]
  eventReviews           EventReview[]                @relation("ReviewerToEventReview")
  feedback               Feedback[]
  financialTransactions  FinancialTransaction[]
  following              Follow[]                     @relation("UserFollowing")
  followers              Follow[]                     @relation("UserFollowers")
  loyaltyMemberships     LoyaltyProgramMember[]
  marketingCampaigns     MarketingCampaign[]
  receivedCampaigns      MarketingCampaignRecipient[]
  marketingPreferences   MarketingPreference?
  nfcCards               NFCCard[]
  nfcDevices             NfcDevice[]
  nfcDeviceScans         NFCDeviceScan[]
  nfcTerminalSettings    NFCTerminalSettings?
  notifications          Notification[]
  orders                 Order[]
  organizerVerification  OrganizerVerification?
  partner                Partner?
  partnerNFCTransactions PartnerNFCTransaction[]
  partnerReviews         PartnerReview[]
  products               Product[]
  productReviews         ProductReview[]
  productTransactions    ProductTransaction[]
  promotions             Promotion[]
  reviews                Review[]
  services               Service[]
  subscriptionHistory    SubscriptionHistory[]
  ownedTeams             Team[]                       @relation("TeamOwner")
  teamInvitations        TeamInvitation[]             @relation("InvitedUser")
  teamMemberships        TeamMember[]                 @relation("TeamMember")
  tickets                Ticket[]
  managedBy              User?                        @relation("UserManagement", fields: [managedById], references: [id])
  managedUsers           User[]                       @relation("UserManagement")
  vendorNFCTransactions  VendorNFCTransaction[]
  vendorOrders           VendorOrder[]
  vendorProfile          VendorProfile?
  vendorReviews          VendorReview[]
  vendorVerification     VendorVerification?
  waitlists              Waitlist[]
  walletSettings         WalletSettings?
  webhooks               Webhook[]
  withdrawals            Withdrawal[]
  accounts               Account[]
  TwoFactorConfirmation  TwoFactorConfirmation?
  // Elite Communication System relations
  eliteCommunications    EliteCommunication[]
  attendeeProfiles       AttendeeProfile[]
  createdChatRooms       ChatRoom[]
  chatRoomMemberships    ChatRoomMember[]
  sentChatMessages       ChatMessage[]
  favorites              Favorite[]
  supportTickets         SupportTicket[]
  supportComments        SupportComment[]
  // Referral system relations
  referralLinks          ReferralLink[]
  referrerReferrals      Referral[]       @relation("ReferrerReferrals")
  refereeReferrals       Referral[]       @relation("RefereeReferrals")
  // Matching system relations
  matchingFeedback       MatchingFeedback[] @relation("UserMatchingFeedback")
  receivedMatchingFeedback MatchingFeedback[] @relation("MatchedUserFeedback")
  matchingAnalytics      MatchingAnalytics[]
  // Audience segmentation relations
  createdAudienceSegments AudienceSegment[]
  audienceSegments       AudienceSegment[] @relation("AudienceSegmentUsers")
  // Cross promotion relations
  createdCrossPromotions CrossPromotion[] @relation("CreatedCrossPromotions")
  respondedCrossPromotions CrossPromotion[] @relation("RespondedCrossPromotions")
  // Promotion strategy relations
  promotionStrategies    PromotionStrategy[]
  // Partner product relations
  partnerProductReviews  PartnerProductReview[]

  @@index([createdAt])
  @@index([emailVerified])
  @@index([subscriptionTier])
  @@index([role])
}

model Event {
  id                 String                 @id @default(cuid())
  title              String                 @unique
  location           String
  createdAt          DateTime               @default(now())
  updatedAt          DateTime               @updatedAt
  userId             String                 @map("user_id")
  endTime            String                 @map("time_end")
  startTime          String                 @map("time_start")
  description        String
  imagePath          String?
  category           EventCategory
  eventType          EventType
  status             EventStatus            @default(Draft)
  endDate            DateTime               @map("date_end")
  startDate          DateTime               @map("date_start")
  venue              String
  ageRestrictionId   String?                @unique
  timeZone           String?                @default("UTC+2")
  metadata           Json?
  teamId             String?
  hasStadiumSeating  Boolean                @default(false)
  advancedAnalytics  AdvancedAnalytics[]
  analytics          AnalyticsEntry[]
  attendance         Attendance[]
  comments           Comment[]
  engagements        Engagement[]
  ageRestriction     AgeRestriction?        @relation("AgeRestrictionToEvent", fields: [ageRestrictionId], references: [id])
  team               Team?                  @relation(fields: [teamId], references: [id])
  user               User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  featuring          EventFeaturing[]
  eventPartners      EventPartner[]
  payouts            EventPayout[]
  promotion          EventPromotion?
  eventReviews       EventReview[]
  vendors            EventVendor[]
  feedback           Feedback[]
  marketingCampaigns MarketingCampaign[]
  orders             Order[]
  posDeviceRentals   POSDeviceRental[]
  ParkingManagement  ParkingManagement?     @relation("ParkingManagementToEvent")
  partnerPromotions  PartnerPromotion[]
  products           Product[]
  promotions         Promotion[]
  reviews            Review[]
  Seat               Seat[]
  seatGroups         SeatGroup[]
  seoSettings        SeoSettings?           @relation("SeoSettingsToEvent")
  services           Service[]
  socialMediaPosts   SocialMediaPost[]
  socialSettings     SocialSettings?        @relation("SocialSettingsToEvent")
  sponsors           Sponsor[]
  tickets            Ticket[]
  nfcTransactions    VendorNFCTransaction[]
  waitlist           Waitlist[]
  audienceData       AudienceData[]
  promotionData      PromotionData[]
  ticketSales        TicketSales[]
  tags               Tag[]                  @relation("EventToTag")
  // Elite Communication System relations
  eliteCommunications EliteCommunication[]
  attendeeProfiles   AttendeeProfile[]
  chatRooms          ChatRoom[]
  messages           Message[]
  meetingRequests    MeetingRequest[]
  favorites          Favorite[]
  supportTickets     SupportTicket[]
  // Referral system relations
  referralLinks      ReferralLink[]
  referrals          Referral[]
  // Matching system relations
  matchingFeedback   MatchingFeedback[]
  matchingAnalytics  MatchingAnalytics[]
  // Cross promotion relations
  sourcePromotions   CrossPromotion[] @relation("SourceEventPromotions")
  targetPromotions   CrossPromotion[] @relation("TargetEventPromotions")
  // Promotion strategy relations
  promotionStrategies PromotionStrategy[]

  @@index([startDate])
  @@index([endDate])
  @@index([category])
  @@index([status])
  @@index([eventType])
  @@index([userId])
  @@index([teamId])
}

model ParkingManagement {
  id                  String  @id @default(uuid())
  totalSpaces         Int
  reservedSpaces      Int
  pricePerHour        Float?
  isFree              Boolean @default(false)
  reservationRequired Boolean @default(false)
  description         String?
  eventId             String? @unique
  event               Event?  @relation("ParkingManagementToEvent", fields: [eventId], references: [id])

  @@index([totalSpaces])
}

model AgeRestriction {
  id          String  @id @default(uuid())
  minAge      Int?
  maxAge      Int?
  ageGroups   String?
  description String?
  event       Event?  @relation("AgeRestrictionToEvent")

  @@index([minAge])
}

model TicketSales {
  id          Int      @id @default(autoincrement())
  type        String
  sold        Int
  revenue     Float
  targetSales Int      @map("target_sales")
  price       Float
  eventId     String
  ticketId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  event       Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  ticket      Ticket?  @relation(fields: [ticketId], references: [id])

  @@index([eventId])
  @@index([ticketId])
  @@index([type])
  @@map("ticket_sales")
}

model AnalyticsEntry {
  id                String   @id @default(cuid())
  eventId           String
  date              DateTime
  ticketsSold       Int
  ticketsUnsold     Int
  refunds           Int
  newCustomers      Int
  existingCustomers Int
  engagements       Int
  revenue           Float
  costs             Float
  profit            Float
  event             Event    @relation(fields: [eventId], references: [id])

  @@index([date])
  @@index([eventId])
}

model AdvancedAnalytics {
  id                      String   @id @default(cuid())
  eventId                 String
  date                    DateTime
  uniqueVisitors          Int
  averageSessionDuration  Float
  bounceRate              Float
  conversionRate          Float
  revenuePerTicket        Float
  customerAcquisitionCost Float
  customerLifetimeValue   Float
  netPromoterScore        Float?
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
  event                   Event    @relation(fields: [eventId], references: [id])

  @@index([date])
  @@index([eventId])
}

model Order {
  id            String      @id @default(uuid())
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  userId        String
  eventId       String
  pricePaid     Float
  status        OrderStatus @default(Pending)
  totalPrice    Float
  checkInStatus String?
  checkInTime   DateTime?
  customerEmail String
  customerName  String
  customerPhone String
  notes         String?
  paymentMethod String?
  event         Event       @relation(fields: [eventId], references: [id])
  user          User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments      Payment[]
  tickets       Ticket[]    @relation("OrderToTicket")

  @@index([createdAt])
  @@index([eventId])
  @@index([userId])
  @@index([status])
}

model Ticket {
  price            Float
  eventId          String
  userId           String
  isUsed           Boolean       @default(false)
  email            String
  isFree           Boolean       @default(false)
  regularPrice     Float
  regularSeats     Int
  totalSeats       Int
  vipPrice         Float
  vipSeats         Int
  vvipPrice        Float?
  vvipSeats        Int
  id               String        @id @default(uuid())
  orderId          String
  createdAt        DateTime      @default(now())
  quantity         Int
  totalPrice       Int
  updatedAt        DateTime      @updatedAt
  type             TicketType
  saleEndTime      String        @map("time_end")
  saleStartTime    String        @map("time_start")
  isAvailable      Boolean       @default(true)
  specialGuestName String
  specialGuestType String
  qrCodeData       String
  scannedAt        DateTime?
  scannedBy        String?
  seatId           String?       @unique
  description      String?
  attendance       Attendance[]
  NFCTag           NFCTag?
  event            Event         @relation(fields: [eventId], references: [id], onDelete: Cascade)
  seat             Seat?         @relation("TicketSeat", fields: [seatId], references: [id])
  user             User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  TicketUsage      TicketUsage[]
  ticketSales      TicketSales[]
  Order            Order[]       @relation("OrderToTicket")

  @@index([isAvailable])
  @@index([eventId])
  @@index([userId])
  @@index([isUsed])
}

model TicketUsage {
  id        String   @id @default(uuid())
  ticketId  String
  usedAt    DateTime @default(now())
  location  String
  scannedBy String
  ticket    Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)
}

model Attendance {
  id          String   @id @default(uuid())
  eventId     String
  ticketId    String
  checkInTime DateTime @default(now())
  location    String?
  scannedBy   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  event       Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  ticket      Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  @@index([eventId])
  @@index([ticketId])
  @@index([checkInTime])
}

/// Payment now belongs directly to an Order.
model Payment {
  id        String        @id @default(uuid())
  method    String
  details   Json
  amount    Float
  currency  String
  status    PaymentStatus
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  orderId   String
  order     Order         @relation(fields: [orderId], references: [id], onDelete: Cascade)
}

model Seat {
  id         String       @id @default(uuid())
  row        String
  number     Int
  section    String
  category   SeatCategory
  isReserved Boolean      @default(false)
  isOccupied Boolean      @default(false)
  eventId    String
  accessible Boolean      @default(false)
  groupId    String?
  label      String?
  notes      String?
  restricted Boolean      @default(false)
  venueType  String?
  x          Float?
  y          Float?
  event      Event        @relation(fields: [eventId], references: [id], onDelete: Cascade)
  group      SeatGroup?   @relation(fields: [groupId], references: [id])
  ticket     Ticket?      @relation("TicketSeat")

  @@index([row])
  @@index([number])
  @@index([section])
  @@index([category])
  @@index([isReserved])
  @@index([isOccupied])
  @@index([groupId])
  @@index([accessible])
}

model SeatGroup {
  id        String  @id @default(uuid())
  name      String
  type      String
  capacity  Int
  eventId   String
  section   String
  x         Float?
  y         Float?
  notes     String?
  venueType String?
  seats     Seat[]
  event     Event   @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([eventId])
  @@index([section])
  @@index([type])
}

model NFCTag {
  id          String    @id @default(cuid())
  uid         String    @unique
  isActive    Boolean   @default(true)
  lastScanned DateTime?
  ticketId    String?   @unique
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  ticket      Ticket?   @relation(fields: [ticketId], references: [id])

  @@index([uid])
  @@index([isActive])
}

model NFCCard {
  id              String                 @id @default(cuid())
  uid             String                 @unique
  isActive        Boolean                @default(true)
  lastUsed        DateTime?
  userId          String?
  createdAt       DateTime               @default(now())
  updatedAt       DateTime               @updatedAt
  status          String                 @default("active")
  balance         Float                  @default(0)
  assignedTo      String?
  eventId         String                 @default("")
  user            User?                  @relation(fields: [userId], references: [id], onDelete: Restrict)
  nfcTransactions VendorNFCTransaction[]

  @@index([uid])
  @@index([isActive])
  @@index([userId])
  @@index([eventId])
  @@index([status])
}

model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model VerificationToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
  @@map("verification_token")
}

model PasswordResetToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
  @@map("password_reset_token")
}

model TwoFactorToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
  @@map("two_factor_token")
}

model TwoFactorConfirmation {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("two_factor_confirmation")
}

model NfcDevice {
  id        String          @id @default(cuid())
  userId    String
  type      NFCDeviceType
  status    NFCDeviceStatus @default(ACTIVE)
  balance   Float           @default(0)
  lastUsed  DateTime?
  issuedAt  DateTime        @default(now())
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  metadata  Json?
  user      User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  scans     NFCDeviceScan[]

  @@index([userId])
}

model NFCDeviceScan {
  id         String    @id @default(cuid())
  deviceId   String
  userId     String
  terminalId String?
  vendorId   String?
  eventId    String?
  timestamp  DateTime  @default(now())
  metadata   Json?
  device     NfcDevice @relation(fields: [deviceId], references: [id], onDelete: Cascade)
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([deviceId])
  @@index([userId])
  @@index([timestamp])
}

model NFCProductPricing {
  id          String        @id @default(cuid())
  deviceType  NFCDeviceType
  name        String
  description String?
  price       Float
  currency    String        @default("USD")
  isActive    Boolean       @default(true)
  imageUrl    String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  @@unique([deviceType, currency])
  @@index([deviceType])
  @@index([isActive])
}

model NFCTerminalSettings {
  id                   String    @id @default(cuid())
  vendorId             String    @unique
  terminalName         String    @default("Main Terminal")
  offlineMode          Boolean   @default(false)
  autoSync             Boolean   @default(true)
  notificationsEnabled Boolean   @default(true)
  autoPrint            Boolean   @default(false)
  deviceId             String    @unique
  lastSyncTime         DateTime?
  softwareVersion      String?   @default("1.0.0")
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt
  vendor               User      @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  @@index([vendorId])
}

model NFCSystemSettings {
  id                      String   @id @default(cuid())
  eventId                 String   @unique
  systemName              String
  currencySymbol          String
  defaultLanguage         String
  maxTransactionAmount    Float
  requirePinForHighValue  Boolean
  highValueThreshold      Float?
  cardLockoutThreshold    Int
  offlineModeEnabled      Boolean
  maxOfflineTransactions  Int?
  offlineTransactionLimit Float?
  syncInterval            Int?
  receiptEnabled          Boolean
  analyticsEnabled        Boolean
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
}

model Sponsor {
  id        String          @id @default(cuid())
  name      String
  logo      String?
  website   String?
  tier      SponsorshipTier
  amount    Float
  eventId   String
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt
  event     Event           @relation(fields: [eventId], references: [id])

  @@index([tier])
  @@index([eventId])
}

model FinancialTransaction {
  id                   String                 @id @default(cuid())
  userId               String
  amount               Float
  type                 TransactionType
  description          String?
  relatedTransactionId String?
  status               TransactionStatus      @default(COMPLETED)
  metadata             Json?
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  relatedTransaction   FinancialTransaction?  @relation("RelatedTransactions", fields: [relatedTransactionId], references: [id])
  relatedTransactions  FinancialTransaction[] @relation("RelatedTransactions")
  user                 User                   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([type])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model WalletSettings {
  id              String   @id @default(cuid())
  userId          String   @unique
  defaultCurrency String   @default("USD")
  lowBalanceAlert Float?
  autoTopup       Boolean  @default(false)
  autoTopupAmount Float?
  autoTopupMethod String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model Engagement {
  id        String   @id @default(cuid())
  eventId   String
  clicks    Int      @default(0)
  views     Int      @default(0)
  shares    Int      @default(0)
  likes     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  event     Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([eventId])
}

model Review {
  id        String   @id @default(cuid())
  content   String
  rating    Int
  userId    String
  eventId   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  event     Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([eventId])
  @@index([rating])
}

model EventReview {
  id         String   @id @default(cuid())
  eventId    String
  reviewerId String
  status     String
  comments   String?
  reviewedAt DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  event      Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  reviewer   User     @relation("ReviewerToEventReview", fields: [reviewerId], references: [id], onDelete: Cascade)

  @@index([eventId])
  @@index([reviewerId])
  @@index([status])
  @@index([createdAt])
}

model Tag {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  events      Event[]  @relation("EventToTag")

  @@index([name])
}

model SystemSetting {
  key       String   @id
  value     Json
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model ABTest {
  id          String        @id @default(cuid())
  name        String
  description String?
  status      ABTestStatus  @default(ACTIVE)
  startDate   DateTime      @default(now())
  endDate     DateTime?
  winner      String?
  variantAId  String        @unique
  variantBId  String        @unique
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  variantA    ABTestVariant @relation("VariantA", fields: [variantAId], references: [id], onDelete: Cascade)
  variantB    ABTestVariant @relation("VariantB", fields: [variantBId], references: [id], onDelete: Cascade)
  events      ABTestEvent[]

  @@index([status])
  @@index([startDate])
  @@index([endDate])
}

model ABTestVariant {
  id                String   @id @default(cuid())
  name              String
  description       String?
  trafficPercentage Float    @default(50)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  testA             ABTest?  @relation("VariantA")
  testB             ABTest?  @relation("VariantB")
}

model ABTestEvent {
  id           String          @id @default(cuid())
  experimentId String
  variant      String
  type         ABTestEventType
  userId       String?
  value        Float?
  createdAt    DateTime        @default(now())
  experiment   ABTest          @relation(fields: [experimentId], references: [id], onDelete: Cascade)

  @@index([experimentId])
  @@index([variant])
  @@index([type])
  @@index([userId])
  @@index([createdAt])
}

model Follow {
  id          String   @id @default(cuid())
  followerId  String
  followingId String
  createdAt   DateTime @default(now())
  follower    User     @relation("UserFollowing", fields: [followerId], references: [id])
  following   User     @relation("UserFollowers", fields: [followingId], references: [id])

  @@unique([followerId, followingId])
  @@index([createdAt])
}

model MarketingPreference {
  id         String  @id @default(cuid())
  userId     String  @unique
  emailOptIn Boolean @default(true)
  smsOptIn   Boolean @default(false)
  pushOptIn  Boolean @default(true)
  user       User    @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model MarketingCampaign {
  id              String                       @id @default(cuid())
  name            String
  description     String?
  content         String
  eventId         String?
  createdAt       DateTime                     @default(now())
  updatedAt       DateTime                     @updatedAt
  sentAt          DateTime?
  audienceSegment String?
  audienceType    String?
  previewText     String?
  scheduledDate   DateTime?
  status          String                       @default("DRAFT")
  subject         String?
  userId          String
  type            String
  event           Event?                       @relation(fields: [eventId], references: [id])
  user            User                         @relation(fields: [userId], references: [id])
  recipients      MarketingCampaignRecipient[]

  @@index([createdAt])
  @@index([eventId])
  @@index([type])
  @@index([userId])
  @@index([status])
}

model MarketingCampaignRecipient {
  id         String            @id @default(cuid())
  campaignId String
  userId     String
  sentAt     DateTime
  openedAt   DateTime?
  clickedAt  DateTime?
  emailId    String?
  campaign   MarketingCampaign @relation(fields: [campaignId], references: [id])
  email      Email?            @relation(fields: [emailId], references: [id])
  user       User              @relation(fields: [userId], references: [id])

  @@unique([campaignId, userId])
  @@index([sentAt])
  @@index([emailId])
}

model EventPromotion {
  id           String                 @id @default(cuid())
  eventId      String                 @unique
  tier         PromotionTier
  startDate    DateTime
  endDate      DateTime
  budget       Float
  impressions  Int                    @default(0)
  clicks       Int                    @default(0)
  conversions  Int                    @default(0)
  createdAt    DateTime               @default(now())
  updatedAt    DateTime               @updatedAt
  event        Event                  @relation(fields: [eventId], references: [id], onDelete: Cascade)
  performances PromotionPerformance[]

  @@index([startDate])
  @@index([endDate])
  @@index([tier])
}

model Promotion {
  id             String   @id @default(cuid())
  eventId        String
  type           String
  startDate      DateTime
  endDate        DateTime
  budget         Float
  targetAudience String?
  channels       String?
  status         String   @default("Active")
  createdBy      String
  impressions    Int      @default(0)
  clicks         Int      @default(0)
  conversions    Int      @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  creator        User     @relation(fields: [createdBy], references: [id], onDelete: Cascade)
  event          Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  strategies     PromotionStrategyPromotion[]

  @@index([eventId])
  @@index([type])
  @@index([status])
  @@index([createdBy])
}

model PromotionPerformance {
  id          String         @id @default(cuid())
  promotionId String
  date        DateTime
  impressions Int
  clicks      Int
  conversions Int
  spend       Float
  promotion   EventPromotion @relation(fields: [promotionId], references: [id])

  @@index([date])
}

model PromotionStrategy {
  id             String                    @id @default(cuid())
  eventId        String
  userId         String
  name           String
  description    String?
  budget         Float
  targetAudience String?
  channels       String[]
  startDate      DateTime?
  endDate        DateTime?
  goals          Json?
  isAutomated    Boolean                   @default(false)
  status         String                    @default("DRAFT")
  createdAt      DateTime                  @default(now())
  updatedAt      DateTime                  @updatedAt
  event          Event                     @relation(fields: [eventId], references: [id], onDelete: Cascade)
  user           User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  recommendations PromotionRecommendation[]
  promotions     PromotionStrategyPromotion[]

  @@index([eventId])
  @@index([userId])
  @@index([status])
}

model PromotionRecommendation {
  id             String           @id @default(cuid())
  strategyId     String
  type           String
  budget         Float
  startDate      DateTime
  endDate        DateTime
  channels       String[]
  targetAudience String?
  reasoning      String?
  status         String           @default("PENDING")
  notes          String?
  implementedAt  DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  strategy       PromotionStrategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@index([strategyId])
  @@index([status])
}

model PromotionStrategyPromotion {
  id         String           @id @default(cuid())
  strategyId String
  promotionId String
  createdAt  DateTime         @default(now())
  strategy   PromotionStrategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)
  promotion  Promotion        @relation(fields: [promotionId], references: [id], onDelete: Cascade)

  @@unique([strategyId, promotionId])
  @@index([strategyId])
  @@index([promotionId])
}

model Feedback {
  id        String   @id @default(cuid())
  eventId   String
  userId    String
  rating    Int
  comment   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  event     Event    @relation(fields: [eventId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@index([eventId])
  @@index([userId])
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  type      String
  message   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([isRead])
}

model Waitlist {
  id        String   @id @default(cuid())
  eventId   String
  userId    String
  position  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  event     Event    @relation(fields: [eventId], references: [id])
  user      User     @relation(fields: [userId], references: [id])

  @@unique([eventId, userId])
  @@index([position])
}

model Product {
  id                  String                     @id @default(cuid())
  name                String
  description         String?
  price               Float
  userId              String
  eventId             String?
  vendorId            String?
  createdAt           DateTime                   @default(now())
  updatedAt           DateTime                   @updatedAt
  about               String?
  category            ProductCategory
  color               String?
  dimensions          String
  imagePath           String?
  material            String
  productType         ProductType
  status              ProductStatus              @default(Draft)
  stockQuantity       Int
  weight              Float
  comments            Comment[]
  orderItems          OrderItem[]
  event               Event?                     @relation(fields: [eventId], references: [id])
  user                User                       @relation(fields: [userId], references: [id], onDelete: Cascade)
  vendor              VendorProfile?             @relation(fields: [vendorId], references: [id])
  advancedAnalytics   ProductAdvancedAnalytics[]
  analytics           ProductAnalytics[]
  reviews             ProductReview[]
  transactions        ProductTransaction[]
  nfcTransactionItems VendorNFCTransactionItem[]

  @@index([name])
  @@index([userId])
  @@index([eventId])
  @@index([vendorId])
}

model ProductAnalytics {
  id             String   @id @default(cuid())
  productId      String
  date           DateTime
  unitsSold      Int
  revenue        Float
  costs          Float
  profit         Float
  views          Int
  addToCartCount Int
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  product        Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([date])
  @@index([productId])
}

model ProductAdvancedAnalytics {
  id                      String   @id @default(cuid())
  productId               String
  date                    DateTime
  conversionRate          Float
  averageOrderValue       Float
  customerAcquisitionCost Float
  customerLifetimeValue   Float
  returnRate              Float
  netPromoterScore        Float?
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt
  product                 Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@index([date])
  @@index([productId])
}

model ProductTransaction {
  id          String          @id @default(cuid())
  productId   String
  userId      String
  quantity    Int
  unitPrice   Float
  totalAmount Float
  type        TransactionType
  description String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  product     Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  user        User            @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([productId])
  @@index([userId])
  @@index([type])
}

model Service {
  id          String    @id @default(cuid())
  name        String
  description String?
  price       Float
  duration    Int
  userId      String
  eventId     String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  comments    Comment[]
  event       Event?    @relation(fields: [eventId], references: [id])
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([name])
  @@index([userId])
  @@index([eventId])
}

model Comment {
  id        String   @id @default(cuid())
  content   String
  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  eventId   String?
  productId String?
  serviceId String?
  event     Event?   @relation(fields: [eventId], references: [id], onDelete: Cascade)
  product   Product? @relation(fields: [productId], references: [id], onDelete: Cascade)
  service   Service? @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([eventId])
  @@index([productId])
  @@index([serviceId])
}

model PromotionData {
  id          Int      @id @default(autoincrement())
  impressions Int
  clicks      Int
  conversions Int
  spend       Float
  roi         Float
  cpc         Float
  ctr         Float
  eventId     String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  date        DateTime
  event       Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([eventId])
  @@index([date])
  @@map("promotion_data")
}

model AudienceData {
  id         Int      @id @default(autoincrement())
  age        String
  percentage Float
  engagement Float
  eventId    String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  event      Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([eventId])
  @@index([age])
  @@map("audience_data")
}

model ApiKey {
  id              String        @id @default(cuid())
  key             String        @unique
  name            String
  userId          String
  permissions     String[]
  lastUsed        DateTime?
  expiresAt       DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  rateLimit       Int           @default(100)
  usageCount      Int           @default(0)
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  apiKeyUsageLogs ApiKeyUsage[]

  @@index([userId])
  @@index([key])
}

model ApiKeyUsage {
  id        String   @id @default(cuid())
  apiKeyId  String
  endpoint  String
  method    String
  status    Int
  timestamp DateTime @default(now())
  ipAddress String?
  userAgent String?
  apiKey    ApiKey   @relation(fields: [apiKeyId], references: [id], onDelete: Cascade)

  @@index([apiKeyId])
  @@index([timestamp])
}

model ApiAlert {
  id         String    @id @default(cuid())
  type       String
  severity   String
  message    String
  details    String
  timestamp  DateTime  @default(now())
  apiKeyId   String?
  userId     String?
  resolved   Boolean   @default(false)
  resolvedAt DateTime?
  resolvedBy String?
  resolution String?

  @@index([timestamp])
  @@index([type])
  @@index([severity])
  @@index([apiKeyId])
  @@index([userId])
}

model ActivityLog {
  id          String   @id @default(cuid())
  userId      String?
  adminId     String?
  action      String
  resource    String?
  resourceId  String?
  details     Json?
  ipAddress   String?
  userAgent   String?
  timestamp   DateTime @default(now())
  success     Boolean  @default(true)
  errorMessage String?

  @@index([userId])
  @@index([adminId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@index([success])
}

model UserInvitation {
  id          String           @id @default(cuid())
  email       String
  role        UserRole
  invitedBy   String
  token       String           @unique
  password    String
  status      InvitationStatus @default(PENDING)
  expiresAt   DateTime
  acceptedAt  DateTime?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@index([email])
  @@index([token])
  @@index([status])
  @@index([invitedBy])
  @@index([expiresAt])
}

model Email {
  id         String                       @id @default(cuid())
  from       String
  to         String?
  subject    String
  preview    String
  date       DateTime
  read       Boolean                      @default(false)
  category   String
  externalId String?
  userId     String
  createdAt  DateTime                     @default(now())
  updatedAt  DateTime                     @updatedAt
  user       User                         @relation(fields: [userId], references: [id], onDelete: Cascade)
  recipients MarketingCampaignRecipient[]

  @@index([category])
}

model NewsletterSubscriber {
  id               String           @id @default(cuid())
  email            String
  firstName        String?
  lastName         String?
  interests        String[]
  status           SubscriberStatus @default(ACTIVE)
  subscriptionDate DateTime         @default(now())
  unsubscribedAt   DateTime?
  lastOpenedAt     DateTime?
  organizerId      String
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  @@unique([email, organizerId])
  @@index([email])
  @@index([status])
  @@index([organizerId])
  @@index([createdAt])
  @@index([lastOpenedAt])
}

model EmailTemplate {
  id          String   @id @default(cuid())
  name        String
  type        String
  html        String
  organizerId String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([type])
  @@index([organizerId])
  @@index([createdAt])
}

model BankAccount {
  id               String       @id @default(cuid())
  userId           String
  bankName         String
  accountNumber    String
  accountName      String
  branchCode       String?
  swiftCode        String?
  routingNumber    String?
  isDefault        Boolean      @default(false)
  isVerified       Boolean      @default(false)
  verificationDate DateTime?
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  user             User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  withdrawals      Withdrawal[]

  @@index([userId])
  @@index([isDefault])
  @@index([isVerified])
}

model Withdrawal {
  id            String           @id @default(cuid())
  userId        String
  bankAccountId String
  amount        Float
  status        WithdrawalStatus @default(Pending)
  requestDate   DateTime         @default(now())
  processedDate DateTime?
  notes         String?
  adminNotes    String?
  reference     String?
  transactionId String?
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  eventPayoutId String?
  bankAccount   BankAccount      @relation(fields: [bankAccountId], references: [id], onDelete: Cascade)
  eventPayout   EventPayout?     @relation(fields: [eventPayoutId], references: [id])
  user          User             @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([bankAccountId])
  @@index([status])
  @@index([requestDate])
  @@index([eventPayoutId])
}

model OrganizerVerification {
  id                  String             @id @default(cuid())
  userId              String             @unique
  businessName        String
  businessType        String
  registrationNumber  String?
  taxPayerIdNumber    String?
  phoneNumber         String
  alternativeEmail    String?
  website             String?
  physicalAddress     String
  city                String
  province            String
  postalCode          String?
  idDocumentPath      String?
  idDocumentType      String?
  businessLicensePath String?
  taxCertificatePath  String?
  status              VerificationStatus @default(PENDING)
  verifiedAt          DateTime?
  rejectionReason     String?
  eventTypes          String?
  experience          String?
  previousEvents      String?
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt
  user                User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([status])
  @@index([createdAt])
}

model SeoSettings {
  id          String   @id @default(cuid())
  title       String?
  description String?
  keywords    String[]
  eventId     String   @unique
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  event       Event    @relation("SeoSettingsToEvent", fields: [eventId], references: [id], onDelete: Cascade)

  @@index([eventId])
}

model SocialSettings {
  id                  String   @id @default(cuid())
  facebookTitle       String?
  facebookDescription String?
  twitterTitle        String?
  twitterDescription  String?
  ogImage             String?
  eventId             String   @unique
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  event               Event    @relation("SocialSettingsToEvent", fields: [eventId], references: [id], onDelete: Cascade)

  @@index([eventId])
}

model Team {
  id          String           @id @default(cuid())
  name        String
  description String?
  ownerId     String
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  events      Event[]
  owner       User             @relation("TeamOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  invitations TeamInvitation[]
  members     TeamMember[]

  @@index([ownerId])
  @@index([createdAt])
}

model TeamMember {
  id          String         @id @default(cuid())
  teamId      String
  userId      String
  role        TeamMemberRole
  permissions Json?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  team        Team           @relation(fields: [teamId], references: [id], onDelete: Cascade)
  user        User           @relation("TeamMember", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([teamId, userId])
  @@index([teamId])
  @@index([userId])
}

model TeamInvitation {
  id          String           @id @default(cuid())
  teamId      String
  email       String
  role        TeamMemberRole
  status      InvitationStatus @default(PENDING)
  token       String           @unique
  invitedById String
  invitedId   String?
  expiresAt   DateTime
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  invitedBy   User             @relation("InvitedUser", fields: [invitedById], references: [id], onDelete: Cascade)
  team        Team             @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@index([teamId])
  @@index([email])
  @@index([token])
  @@index([status])
  @@index([expiresAt])
}

model EventPayout {
  id               String       @id @default(cuid())
  eventId          String
  userId           String
  amount           Float
  status           PayoutStatus @default(Pending)
  commissionAmount Float
  commissionRate   Float
  totalTicketSales Float
  totalRevenue     Float
  requestDate      DateTime     @default(now())
  processedDate    DateTime?
  notes            String?
  adminNotes       String?
  reference        String?
  transactionId    String?
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  event            Event        @relation(fields: [eventId], references: [id], onDelete: Cascade)
  user             User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  withdrawals      Withdrawal[]

  @@index([eventId])
  @@index([userId])
  @@index([status])
  @@index([requestDate])
  @@index([processedDate])
}

model EventFeaturing {
  id               String            @id @default(cuid())
  eventId          String
  tier             FeaturingTier
  startDate        DateTime
  endDate          DateTime
  status           FeaturingStatus   @default(PENDING)
  paymentAmount    Decimal
  paymentId        String?
  metadata         Json?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  event            Event             @relation(fields: [eventId], references: [id], onDelete: Cascade)
  socialMediaPosts SocialMediaPost[]

  @@index([eventId])
  @@index([tier])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
}

model SocialMediaPost {
  id                String          @id @default(cuid())
  platform          String
  content           String
  imageUrl          String?
  status            String
  scheduledDate     DateTime?
  publishedDate     DateTime?
  eventId           String?
  engagementMetrics Json?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  featuringId       String?
  event             Event?          @relation(fields: [eventId], references: [id])
  featuring         EventFeaturing? @relation(fields: [featuringId], references: [id])

  @@index([platform])
  @@index([status])
  @@index([eventId])
  @@index([featuringId])
  @@index([scheduledDate])
  @@index([createdAt])
}

model VendorProfile {
  id                     String                 @id @default(cuid())
  userId                 String                 @unique
  businessName           String
  businessType           String
  registrationNumber     String?
  taxPayerIdNumber       String?
  yearEstablished        Int?
  description            String?
  productCategories      String?
  serviceCategories      String?
  specializations        String?
  certifications         String?
  logo                   String?
  bannerImage            String?
  galleryImages          Json?
  email                  String?
  phoneNumber            String?
  alternativePhoneNumber String?
  website                String?
  socialLinks            Json?
  physicalAddress        String?
  city                   String?
  province               String?
  postalCode             String?
  country                String?                @default("Zambia")
  gpsCoordinates         String?
  businessHours          Json?
  acceptedPaymentMethods String?
  bankAccountDetails     Json?
  featured               Boolean                @default(false)
  averageRating          Float?
  totalReviews           Int                    @default(0)
  totalSales             Float                  @default(0)
  totalOrders            Int                    @default(0)
  verificationStatus     VerificationStatus     @default(PENDING)
  verifiedAt             DateTime?
  complianceStatus       String?
  insuranceDetails       String?
  employeeCount          Int?
  businessSize           String?
  yearlyRevenue          String?
  createdAt              DateTime               @default(now())
  updatedAt              DateTime               @updatedAt
  eventParticipations    EventVendor[]
  posDeviceRentals       POSDeviceRental[]
  products               Product[]
  nfcTransactions        VendorNFCTransaction[]
  orders                 VendorOrder[]
  user                   User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  reviews                VendorReview[]

  @@index([userId])
  @@index([verificationStatus])
  @@index([featured])
  @@index([businessName])
  @@index([city])
  @@index([productCategories])
  vendorFeaturings       VendorFeaturing[]
}

model VendorFeaturing {
  id               String            @id @default(cuid())
  vendorId         String
  tier             FeaturingTier
  startDate        DateTime
  endDate          DateTime
  status           FeaturingStatus   @default(PENDING)
  paymentAmount    Decimal
  paymentId        String?
  metadata         Json?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  vendor           VendorProfile     @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  @@index([vendorId])
  @@index([tier])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
}

model VendorVerification {
  id                  String             @id @default(cuid())
  userId              String             @unique
  registrationNumber  String?
  taxPayerIdNumber    String?
  phoneNumber         String
  alternativeEmail    String?
  website             String?
  physicalAddress     String
  city                String
  province            String
  postalCode          String?
  productCategories   String
  businessDescription String
  yearsInBusiness     String?
  idDocumentPath      String
  idDocumentType      String
  businessLicensePath String
  taxCertificatePath  String?
  status              VerificationStatus @default(PENDING)
  reviewedBy          String?
  reviewedAt          DateTime?
  rejectionReason     String?
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @updatedAt
  user                User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([status])
}

model VendorOrder {
  id                 String            @id @default(cuid())
  vendorId           String
  userId             String
  orderNumber        String            @unique
  status             VendorOrderStatus @default(Pending)
  totalAmount        Float
  currency           String            @default("ZMW")
  shippingAddress    String?
  shippingCity       String?
  shippingProvince   String?
  shippingPostalCode String?
  paymentMethod      String
  paymentStatus      PaymentStatus     @default(Pending)
  paymentId          String?
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @updatedAt
  items              OrderItem[]
  user               User              @relation(fields: [userId], references: [id])
  vendor             VendorProfile     @relation(fields: [vendorId], references: [id])

  @@index([vendorId])
  @@index([userId])
}

model OrderItem {
  id         String      @id @default(cuid())
  orderId    String
  productId  String
  quantity   Int
  unitPrice  Float
  totalPrice Float
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  order      VendorOrder @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product    Product     @relation(fields: [productId], references: [id])

  @@index([orderId])
  @@index([productId])
}

model EventVendor {
  id          String              @id @default(cuid())
  eventId     String
  vendorId    String
  status      ParticipationStatus @default(PENDING)
  boothNumber String?
  notes       String?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  event       Event               @relation(fields: [eventId], references: [id], onDelete: Cascade)
  vendor      VendorProfile       @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  @@unique([eventId, vendorId])
  @@index([eventId])
  @@index([vendorId])
}

model VendorReview {
  id          String        @id @default(cuid())
  vendorId    String
  userId      String
  rating      Int
  comment     String?
  isPublished Boolean       @default(true)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  user        User          @relation(fields: [userId], references: [id])
  vendor      VendorProfile @relation(fields: [vendorId], references: [id], onDelete: Cascade)

  @@index([vendorId])
  @@index([userId])
}

model ProductReview {
  id          String   @id @default(cuid())
  productId   String
  userId      String
  rating      Int
  comment     String?
  isPublished Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])

  @@index([productId])
  @@index([userId])
}

model VendorNFCTransaction {
  id          String                     @id @default(cuid())
  vendorId    String
  eventId     String
  cardId      String
  userId      String
  amount      Float
  currency    String                     @default("ZMW")
  status      TransactionStatus          @default(PENDING)
  reference   String?
  notes       String?
  createdAt   DateTime                   @default(now())
  processedAt DateTime?
  nfcCard     NFCCard                    @relation(fields: [cardId], references: [id])
  event       Event                      @relation(fields: [eventId], references: [id])
  user        User                       @relation(fields: [userId], references: [id])
  vendor      VendorProfile              @relation(fields: [vendorId], references: [id])
  products    VendorNFCTransactionItem[]

  @@index([vendorId])
  @@index([eventId])
  @@index([cardId])
  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

model VendorNFCTransactionItem {
  id            String               @id @default(cuid())
  transactionId String
  productId     String
  quantity      Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime             @default(now())
  product       Product              @relation(fields: [productId], references: [id])
  transaction   VendorNFCTransaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@index([transactionId])
  @@index([productId])
}

model FeeConfiguration {
  id              String    @id @default(cuid())
  name            String
  description     String?
  feeType         FeeType
  value           Float
  isPercentage    Boolean   @default(true)
  isActive        Boolean   @default(true)
  appliesTo       String[]
  transactionType String[]
  minAmount       Float?
  maxAmount       Float?
  effectiveFrom   DateTime  @default(now())
  effectiveUntil  DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  createdBy       String?

  @@index([feeType])
  @@index([isActive])
  @@index([effectiveFrom])
  @@index([effectiveUntil])
}

model POSDevice {
  id              String            @id @default(cuid())
  deviceId        String            @unique
  serialNumber    String            @unique
  model           String
  manufacturer    String
  purchaseDate    DateTime
  status          POSDeviceStatus   @default(AVAILABLE)
  lastMaintenance DateTime?
  nextMaintenance DateTime?
  notes           String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  rentals         POSDeviceRental[]

  @@index([status])
  @@index([deviceId])
  @@index([serialNumber])
}

model POSDeviceRental {
  id              String                 @id @default(cuid())
  deviceId        String
  vendorId        String
  eventId         String?
  rentalStartDate DateTime
  rentalEndDate   DateTime?
  status          POSRentalStatus        @default(ACTIVE)
  rentalFee       Float
  depositAmount   Float?
  isReturned      Boolean                @default(false)
  returnDate      DateTime?
  condition       String?
  notes           String?
  createdAt       DateTime               @default(now())
  updatedAt       DateTime               @updatedAt
  device          POSDevice              @relation(fields: [deviceId], references: [id])
  event           Event?                 @relation(fields: [eventId], references: [id])
  vendor          VendorProfile          @relation(fields: [vendorId], references: [id])
  transactions    POSRentalTransaction[]

  @@index([deviceId])
  @@index([vendorId])
  @@index([eventId])
  @@index([status])
  @@index([rentalStartDate])
  @@index([rentalEndDate])
}

model POSRentalTransaction {
  id            String            @id @default(cuid())
  rentalId      String
  amount        Float
  type          String
  status        TransactionStatus @default(PENDING)
  paymentMethod String?
  reference     String?
  notes         String?
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  rental        POSDeviceRental   @relation(fields: [rentalId], references: [id])

  @@index([rentalId])
  @@index([status])
  @@index([type])
}

model TransactionFee {
  id              String   @id @default(cuid())
  transactionId   String
  feeConfigId     String?
  amount          Float
  feeType         FeeType
  isPercentage    Boolean  @default(true)
  percentageValue Float?
  description     String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([transactionId])
  @@index([feeType])
}

model LoginAttempt {
  id           String    @id @default(cuid())
  email        String
  ipAddress    String
  userAgent    String?
  success      Boolean   @default(false)
  createdAt    DateTime  @default(now())
  blockedUntil DateTime?

  @@index([email])
  @@index([ipAddress])
  @@index([createdAt])
  @@index([blockedUntil])
}

model SubscriptionHistory {
  id           String    @id @default(cuid())
  userId       String
  tier         String
  startDate    DateTime
  endDate      DateTime?
  amount       Decimal   @db.Decimal(10, 2)
  billingCycle String
  status       String    @default("ACTIVE")
  paymentId    String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([tier])
  @@index([status])
  @@index([startDate])
  @@index([endDate])
}

model Webhook {
  id            String    @id @default(cuid())
  userId        String
  name          String
  url           String
  events        String[]
  secret        String
  active        Boolean   @default(true)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  lastTriggered DateTime?
  status        String    @default("pending")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([active])
}

model SubscriptionTierPrice {
  id                  String   @id @default(cuid())
  tier                String   @unique
  monthlyPrice        Decimal  @db.Decimal(10, 2)
  yearlyPrice         Decimal  @db.Decimal(10, 2)
  commissionRate      Decimal  @db.Decimal(5, 2)
  maxEvents           Int?
  maxTeamMembers      Int?
  maxEmailCampaigns   Int?
  maxAnalyticsReports Int?
  maxVendorManagement Int?
  isActive            Boolean  @default(true)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  createdBy           String?

  @@index([tier])
  @@index([isActive])
}

model GlobalSettings {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?

  @@index([key])
}

model Partner {
  id                 String                  @id @default(cuid())
  userId             String                  @unique
  businessName       String
  partnerType        PartnerType
  tier               PartnershipTier         @default(BASIC)
  description        String?
  address            String
  city               String
  province           String
  postalCode         String?
  country            String                  @default("Zambia")
  latitude           Float?
  longitude          Float?
  contactName        String
  contactEmail       String
  contactPhone       String
  website            String?
  logo               String?
  profilePicture     String?
  bannerImage        String?
  galleryImages      Json?
  socialLinks        Json?
  businessHours      Json?
  amenities          String[]
  priceRange         String?
  rating             Float?
  totalReviews       Int                     @default(0)
  isVerified         Boolean                 @default(false)
  verifiedAt         DateTime?
  featured           Boolean                 @default(false)
  acceptsNfcPayments Boolean                 @default(false)
  nfcTerminalId      String?
  commissionRate     Float                   @default(5.0)
  createdAt          DateTime                @default(now())
  updatedAt          DateTime                @updatedAt
  eventPartnerships  EventPartner[]
  loyaltyProgram     LoyaltyProgram?
  menuItems          MenuItem[]
  products           PartnerProduct[]
  productCategories  PartnerProductCategory[]
  user               User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  nfcTransactions    PartnerNFCTransaction[]
  promotions         PartnerPromotion[]
  reviews            PartnerReview[]

  @@index([partnerType])
  @@index([tier])
  @@index([city])
  @@index([featured])
  @@index([isVerified])
  @@index([acceptsNfcPayments])
}

model PartnerPromotion {
  id            String   @id @default(cuid())
  partnerId     String
  eventId       String?
  title         String
  description   String
  startDate     DateTime
  endDate       DateTime
  discountValue Float?
  discountType  String?
  promoCode     String?
  isActive      Boolean  @default(true)
  maxUses       Int?
  currentUses   Int      @default(0)
  imageUrl      String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  event         Event?   @relation(fields: [eventId], references: [id])
  partner       Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)

  @@index([partnerId])
  @@index([eventId])
  @@index([isActive])
  @@index([startDate])
  @@index([endDate])
}

model PartnerReview {
  id          String   @id @default(cuid())
  partnerId   String
  userId      String
  rating      Float
  comment     String?
  isPublished Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  partner     Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])

  @@index([partnerId])
  @@index([userId])
  @@index([rating])
}

model PartnerNFCTransaction {
  id            String                      @id @default(cuid())
  partnerId     String
  userId        String
  cardId        String?
  deviceId      String?
  amount        Float
  currency      String                      @default("ZMW")
  status        TransactionStatus           @default(PENDING)
  reference     String?
  receiptUrl    String?
  notes         String?
  metadata      Json?
  createdAt     DateTime                    @default(now())
  processedAt   DateTime?
  loyaltyPoints Int                         @default(0)
  partner       Partner                     @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  user          User                        @relation(fields: [userId], references: [id])
  items         PartnerNFCTransactionItem[]

  @@index([partnerId])
  @@index([userId])
  @@index([cardId])
  @@index([deviceId])
  @@index([status])
  @@index([createdAt])
}

model PartnerNFCTransactionItem {
  id            String                @id @default(cuid())
  transactionId String
  menuItemId    String?
  name          String
  quantity      Int
  unitPrice     Float
  totalPrice    Float
  createdAt     DateTime              @default(now())
  menuItem      MenuItem?             @relation(fields: [menuItemId], references: [id])
  transaction   PartnerNFCTransaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)

  @@index([transactionId])
  @@index([menuItemId])
}

model MenuItem {
  id               String                      @id @default(cuid())
  partnerId        String
  name             String
  description      String?
  category         String
  price            Float
  imageUrl         String?
  isAvailable      Boolean                     @default(true)
  allergens        String[]
  nutritionalInfo  Json?
  preparationTime  Int?
  isPopular        Boolean                     @default(false)
  isVegetarian     Boolean                     @default(false)
  isVegan          Boolean                     @default(false)
  isGlutenFree     Boolean                     @default(false)
  createdAt        DateTime                    @default(now())
  updatedAt        DateTime                    @updatedAt
  partner          Partner                     @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  transactionItems PartnerNFCTransactionItem[]

  @@index([partnerId])
  @@index([category])
  @@index([isAvailable])
  @@index([isPopular])
}

model LoyaltyProgram {
  id                String                 @id @default(cuid())
  partnerId         String                 @unique
  name              String
  description       String?
  pointsPerCurrency Float                  @default(1.0)
  pointsExpiration  Int?
  tiers             Json?
  createdAt         DateTime               @default(now())
  updatedAt         DateTime               @updatedAt
  partner           Partner                @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  members           LoyaltyProgramMember[]

  @@index([partnerId])
}

model LoyaltyProgramMember {
  id           String         @id @default(cuid())
  programId    String
  userId       String
  points       Int            @default(0)
  tier         String         @default("STANDARD")
  joinedAt     DateTime       @default(now())
  lastActivity DateTime?
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  program      LoyaltyProgram @relation(fields: [programId], references: [id], onDelete: Cascade)
  user         User           @relation(fields: [userId], references: [id])

  @@unique([programId, userId])
  @@index([programId])
  @@index([userId])
  @@index([tier])
}

model EventPartner {
  id           String   @id @default(cuid())
  eventId      String
  partnerId    String
  partnerType  String
  specialOffer String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  event        Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  partner      Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)

  @@unique([eventId, partnerId])
  @@index([eventId])
  @@index([partnerId])
  @@index([partnerType])
}

// Elite Communication System Models
model EliteCommunication {
  id               String                    @id @default(cuid())
  userId           String
  eventId          String
  tier             EliteCommunicationTier
  subscriptionType EliteSubscriptionType    @default(PER_EVENT)
  isActive         Boolean                   @default(true)
  purchasePrice    Float?
  expiresAt        DateTime?
  createdAt        DateTime                  @default(now())
  updatedAt        DateTime                  @updatedAt
  user             User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  event            Event                     @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@unique([userId, eventId])
  @@index([userId])
  @@index([eventId])
  @@index([tier])
  @@index([isActive])
}

model AttendeeProfile {
  id                String                    @id @default(cuid())
  userId            String
  eventId           String
  displayName       String
  bio               String?
  company           String?
  role              String?
  industry          String?
  interests         String[]
  networkingGoals   String?
  profilePhoto      String?
  linkedinUrl       String?
  twitterUrl        String?
  websiteUrl        String?
  isDiscoverable    Boolean                   @default(true)
  privacyLevel      AttendeePrivacyLevel      @default(PUBLIC)
  allowMessages     AttendeeMessageSetting    @default(EVERYONE)
  allowMeetings     Boolean                   @default(true)
  timezone          String?
  availableHours    Json?
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt
  user              User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  event             Event                     @relation(fields: [eventId], references: [id], onDelete: Cascade)
  sentMessages      Message[]                 @relation("SenderProfile")
  receivedMessages  Message[]                 @relation("ReceiverProfile")
  sentMeetings      MeetingRequest[]          @relation("SenderProfile")
  receivedMeetings  MeetingRequest[]          @relation("ReceiverProfile")

  @@unique([userId, eventId])
  @@index([userId])
  @@index([eventId])
  @@index([isDiscoverable])
  @@index([industry])
}

model ChatRoom {
  id          String             @id @default(cuid())
  eventId     String
  name        String
  description String?
  roomType    ChatRoomType       @default(PUBLIC)
  isActive    Boolean            @default(true)
  maxMembers  Int?
  createdById String
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  event       Event              @relation(fields: [eventId], references: [id], onDelete: Cascade)
  createdBy   User               @relation(fields: [createdById], references: [id])
  members     ChatRoomMember[]
  messages    ChatMessage[]

  @@index([eventId])
  @@index([roomType])
  @@index([isActive])
  @@index([createdById])
}

model ChatRoomMember {
  id           String    @id @default(cuid())
  chatRoomId   String
  userId       String
  joinedAt     DateTime  @default(now())
  leftAt       DateTime?
  isModerator  Boolean   @default(false)
  isMuted      Boolean   @default(false)
  lastReadAt   DateTime?
  chatRoom     ChatRoom  @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([chatRoomId, userId])
  @@index([chatRoomId])
  @@index([userId])
  @@index([joinedAt])
}

model ChatMessage {
  id         String      @id @default(cuid())
  chatRoomId String
  senderId   String
  content    String
  messageType MessageType @default(TEXT)
  attachments Json?
  isEdited   Boolean     @default(false)
  editedAt   DateTime?
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  chatRoom   ChatRoom    @relation(fields: [chatRoomId], references: [id], onDelete: Cascade)
  sender     User        @relation(fields: [senderId], references: [id], onDelete: Cascade)

  @@index([chatRoomId])
  @@index([senderId])
  @@index([createdAt])
}

model Message {
  id         String         @id @default(cuid())
  senderId   String
  receiverId String
  eventId    String
  content    String
  messageType MessageType   @default(TEXT)
  attachments Json?
  isRead     Boolean        @default(false)
  readAt     DateTime?
  createdAt  DateTime       @default(now())
  updatedAt  DateTime       @updatedAt
  sender     AttendeeProfile @relation("SenderProfile", fields: [senderId], references: [id], onDelete: Cascade)
  receiver   AttendeeProfile @relation("ReceiverProfile", fields: [receiverId], references: [id], onDelete: Cascade)
  event      Event          @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([senderId])
  @@index([receiverId])
  @@index([eventId])
  @@index([isRead])
  @@index([createdAt])
}

model MeetingRequest {
  id                 String              @id @default(cuid())
  senderId           String
  receiverId         String
  eventId            String
  title              String
  description        String?
  proposedStartTime  DateTime
  proposedEndTime    DateTime
  timezone           String
  meetingType        MeetingType         @default(VIRTUAL)
  meetingUrl         String?
  location           String?
  status             MeetingRequestStatus @default(PENDING)
  responseMessage    String?
  respondedAt        DateTime?
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @updatedAt
  sender             AttendeeProfile     @relation("SenderProfile", fields: [senderId], references: [id], onDelete: Cascade)
  receiver           AttendeeProfile     @relation("ReceiverProfile", fields: [receiverId], references: [id], onDelete: Cascade)
  event              Event               @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@index([senderId])
  @@index([receiverId])
  @@index([eventId])
  @@index([status])
  @@index([proposedStartTime])
}

// Missing User Interaction Models
model Favorite {
  id        String   @id @default(cuid())
  userId    String
  eventId   String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  event     Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)

  @@unique([userId, eventId])
  @@index([userId])
  @@index([eventId])
}



model SupportTicket {
  id          String              @id @default(cuid())
  userId      String
  eventId     String?
  subject     String
  description String
  category    SupportCategory
  priority    SupportPriority
  status      SupportTicketStatus @default(OPEN)
  assignedTo  String?
  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt
  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  event       Event?              @relation(fields: [eventId], references: [id], onDelete: SetNull)
  comments    SupportComment[]

  @@index([userId])
  @@index([eventId])
  @@index([status])
  @@index([category])
  @@index([priority])
  @@index([createdAt])
}

model SupportComment {
  id        String        @id @default(cuid())
  ticketId  String
  userId    String?
  isStaff   Boolean       @default(false)
  message   String
  createdAt DateTime      @default(now())
  ticket    SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  user      User?         @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([ticketId])
  @@index([userId])
  @@index([createdAt])
}

enum SupportCategory {
  TECHNICAL
  BILLING
  GENERAL
  REFUND
  EVENT_INQUIRY
}

enum SupportPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum NFCDeviceType {
  CARD
  FABRIC_WRISTBAND
  PAPER_WRISTBAND
  SILICONE_WRISTBAND
  TAG
}

enum NFCDeviceStatus {
  ACTIVE
  DEACTIVATED
  LOST
}

enum UserRole {
  ADMIN
  ORGANIZER
  USER
  VENDOR
  SUPERADMIN
  DEVELOPER
  PARTNER
}

enum OrderStatus {
  Pending
  Completed
  Cancelled
  Refunded
}

enum EventCategory {
  WEDDING
  FUNERAL
  BUSINESS
  MUSIC
  LIFESTYLE_EVENTS
  EDUCATIONAL_EVENTS
  HOLIDAY_CELEBRATIONS
  FASHION_SHOWS
  HEALTH_AND_WELLNESS
  CULTURAL_FESTIVALS
  GAMING_EVENTS
  ENVIRONMENTAL_EVENTS
  TRADE_FAIR
  AGRICULTURAL_AND_COMMECIAL_SHOW
  WEB_DEVELOPMENT
  MARKETING
  TECHNOLOGY
  CONCERTS_AND_CHURCH
  CONFERENCES_AND_WORKSHOPS
  SPORTS_AND_FITNESS
  ARTS_AND_THEATER
  FAMILY_AND_KIDS
  FOOD_AND_DRINK
  CHARITY_AND_FUNDRAISERS
  COMEDY_SHOWS
  NETWORKING_AND_SOCIAL_GATHERINGS
  FILM_SCREENINGS
}

enum ProductCategory {
  ELECTRONICS
  FASHION
  HOME_AND_GARDEN
  SPORTS_AND_OUTDOORS
  TOYS_AND_GAMES
  HEALTH_AND_BEAUTY
  BABY_PRODUCTS
  PET_PRODUCTS
  ART_AND_CRAFTS
  FOOD_AND_BEVERAGES
  OTHER
}

enum ProductType {
  PHYSICAL
  DIGITAL
}

enum ProductStatus {
  Draft
  Onsale
  Cancelled
  Sold
}

enum EventType {
  PHYSICAL
  ONLINE
  HYBRID
}

enum EventStatus {
  Draft
  Published
  Cancelled
  Completed
  Suspended
}

enum TicketType {
  REGULAR
  VIP
  EARLY_BIRD
  GROUP
  VVIP
}

enum SponsorshipTier {
  PLATINUM
  GOLD
  SILVER
  BRONZE
}

enum OrganizerSubscriptionTier {
  ELITE
  PREMIUM
  BASIC
  NONE
}

enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
}

enum TransactionType {
  TICKET_SALE
  ORGANIZER_REVENUE
  PLATFORM_FEE
  REFUND
  WITHDRAWAL
  DEPOSIT
  VENDOR_SALE
  SERVICE_FEE
  COMMISSION
  ADJUSTMENT
  EVENT_PAYOUT
  TRANSFER
  POS_RENTAL_FEE
  PROCESSING_FEE
}

enum MarketingCampaignType {
  EMAIL
  PUSH_NOTIFICATION
  SMS
  SOCIAL_MEDIA
}

enum SubscriberStatus {
  ACTIVE
  UNSUBSCRIBED
  BOUNCED
  COMPLAINED
}

enum PromotionTier {
  STANDARD
  PREMIUM
  ELITE
}

enum PaymentStatus {
  Pending
  Completed
  Failed
  Refunded
  Cancelled
}

enum WithdrawalStatus {
  Pending
  Approved
  Rejected
  Processed
  Failed
}

enum PayoutStatus {
  Pending
  Processing
  Completed
  Failed
  Cancelled
}

enum SeatCategory {
  Regular
  VIP
  VVIP
  Premium
  Box
  Booth
  Table
  Accessible
  RestrictedView
  Standing
}

enum FeaturingTier {
  BASIC
  PREMIUM
  ELITE
}

enum FeaturingStatus {
  PENDING
  ACTIVE
  EXPIRED
  CANCELLED
}

enum ParticipationStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum VendorOrderStatus {
  Pending
  Processing
  Shipped
  Delivered
  Completed
  Cancelled
  Refunded
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
  CANCELLED
}

enum ABTestStatus {
  ACTIVE
  PAUSED
  COMPLETED
}

enum ABTestEventType {
  IMPRESSION
  CLICK
  CONVERSION
}

enum TeamMemberRole {
  ORGANIZER_ADMIN
  ORGANIZER_MANAGER
  ORGANIZER_EDITOR
  ORGANIZER_ANALYST
  ORGANIZER_SUPPORT
}

enum InvitationStatus {
  PENDING
  ACCEPTED
  DECLINED
  EXPIRED
}

enum FeeType {
  PLATFORM_COMMISSION
  PROCESSING_FEE
  POS_RENTAL_FEE
  SERVICE_FEE
  OTHER
}

enum POSDeviceStatus {
  AVAILABLE
  RENTED
  MAINTENANCE
  DAMAGED
  RETIRED
}

enum POSRentalStatus {
  PENDING
  ACTIVE
  COMPLETED
  CANCELLED
}

enum PartnerType {
  HOTEL
  RESTAURANT
  BAR
  NIGHTCLUB
  CAFE
  LOUNGE
  VENUE
  CATERING
  TRANSPORT
  ENTERTAINMENT
  RETAIL
  SERVICE
  OTHER
}

enum PartnerProductType {
  PRODUCT
  SERVICE
  PACKAGE
  EXPERIENCE
}

enum PartnerProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  OUT_OF_STOCK
}

enum PartnershipTier {
  BASIC
  PREMIUM
  ELITE
}

// Elite Communication System Enums
enum EliteCommunicationTier {
  BASIC
  ELITE
  ELITE_PRO
}

enum EliteSubscriptionType {
  PER_EVENT
  MONTHLY
  YEARLY
}

enum AttendeePrivacyLevel {
  PUBLIC
  ELITE_ONLY
  HIDDEN
}

enum AttendeeMessageSetting {
  EVERYONE
  ELITE_ONLY
  NONE
}

enum ChatRoomType {
  PUBLIC
  ELITE_EXCLUSIVE
  ELITE_PRO_EXCLUSIVE
  PRIVATE
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  LINK
}

enum MeetingType {
  VIRTUAL
  IN_PERSON
  HYBRID
}

enum MeetingRequestStatus {
  PENDING
  ACCEPTED
  DECLINED
  CANCELLED
  COMPLETED
}

// Referral Link Model
model ReferralLink {
  id         String   @id @default(cuid())
  code       String   @unique
  eventId    String
  referrerId String
  expiresAt  DateTime?
  views      Int      @default(0)
  clicks     Int      @default(0)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  event      Event    @relation(fields: [eventId], references: [id])
  referrer   User     @relation(fields: [referrerId], references: [id])
  referrals  Referral[]

  @@index([eventId])
  @@index([referrerId])
  @@index([code])
}

// Referral Model
model Referral {
  id             String   @id @default(cuid())
  referrerId     String
  refereeId      String
  eventId        String
  status         String   // e.g., 'PENDING', 'CONVERTED'
  referralLinkId String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  referrer       User     @relation("ReferrerReferrals", fields: [referrerId], references: [id])
  referee        User     @relation("RefereeReferrals", fields: [refereeId], references: [id])
  event          Event    @relation(fields: [eventId], references: [id])
  referralLink   ReferralLink @relation(fields: [referralLinkId], references: [id])

  @@index([referrerId])
  @@index([refereeId])
  @@index([eventId])
  @@index([referralLinkId])
  @@unique([referrerId, refereeId, eventId])
}

// Audience Segment Model
model AudienceSegment {
  id          String   @id @default(cuid())
  name        String
  description String?
  type        String   @default("custom") // 'system' or 'custom'
  criteria    Json?    // Store segmentation criteria as JSON
  userId      String   // Creator of the segment
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  users       User[]   @relation("AudienceSegmentUsers")

  @@index([userId])
  @@index([type])
  @@index([createdAt])
}

// Cross Promotion Model
model CrossPromotion {
  id              String   @id @default(cuid())
  sourceEventId   String
  targetEventId   String
  promotionType   String   @default("MUTUAL") // 'MUTUAL', 'ONE_WAY'
  status          String   @default("PENDING") // 'PENDING', 'ACCEPTED', 'REJECTED', 'ACTIVE', 'COMPLETED', 'CANCELLED'
  message         String?
  responseMessage String?
  createdBy       String
  respondedBy     String?
  respondedAt     DateTime?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  sourceEvent     Event    @relation("SourceEventPromotions", fields: [sourceEventId], references: [id], onDelete: Cascade)
  targetEvent     Event    @relation("TargetEventPromotions", fields: [targetEventId], references: [id], onDelete: Cascade)
  creator         User     @relation("CreatedCrossPromotions", fields: [createdBy], references: [id], onDelete: Cascade)
  responder       User?    @relation("RespondedCrossPromotions", fields: [respondedBy], references: [id], onDelete: SetNull)

  @@index([sourceEventId])
  @@index([targetEventId])
  @@index([status])
  @@index([createdBy])
  @@index([createdAt])
  @@unique([sourceEventId, targetEventId])
}

// Matching Feedback Model
model MatchingFeedback {
  id             String   @id @default(cuid())
  userId         String
  eventId        String
  matchedUserId  String
  rating         Int      // 1-5 rating scale
  feedback       String?
  actionTaken    String?  // e.g., 'messaged', 'scheduled_meeting', 'connected'
  connectionMade Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  user           User     @relation("UserMatchingFeedback", fields: [userId], references: [id])
  matchedUser    User     @relation("MatchedUserFeedback", fields: [matchedUserId], references: [id])
  event          Event    @relation(fields: [eventId], references: [id])

  @@index([userId])
  @@index([eventId])
  @@index([matchedUserId])
  @@index([rating])
  @@index([createdAt])
}

// Matching Analytics Model
model MatchingAnalytics {
  id                    String   @id @default(cuid())
  userId                String
  eventId               String
  totalMatches          Int      @default(0)
  successfulConnections Int      @default(0)
  averageRating         Float?
  lastMatchDate         DateTime?
  profileViews          Int      @default(0)
  messagesReceived      Int      @default(0)
  messagesSent          Int      @default(0)
  meetingsScheduled     Int      @default(0)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  user                  User     @relation(fields: [userId], references: [id])
  event                 Event    @relation(fields: [eventId], references: [id])

  @@unique([userId, eventId])
  @@index([userId])
  @@index([eventId])
  @@index([totalMatches])
  @@index([successfulConnections])
}

// Partner Products/Services Models
model PartnerProduct {
  id                String                    @id @default(cuid())
  partnerId         String
  name              String
  description       String?
  shortDescription  String?
  type              PartnerProductType        @default(PRODUCT)
  category          String
  subcategory       String?
  price             Float
  originalPrice     Float?
  currency          String                    @default("ZMW")
  imageUrl          String?
  galleryImages     Json?
  status            PartnerProductStatus      @default(DRAFT)
  isAvailable       Boolean                   @default(true)
  isFeatured        Boolean                   @default(false)
  isPopular         Boolean                   @default(false)
  stockQuantity     Int?
  minOrderQuantity  Int                       @default(1)
  maxOrderQuantity  Int?
  duration          Int?
  durationUnit      String?
  location          String?
  inclusions        String[]
  exclusions        String[]
  requirements      String[]
  tags              String[]
  seoTitle          String?
  seoDescription    String?
  seoKeywords       String[]
  rating            Float?                    @default(0)
  totalReviews      Int                       @default(0)
  totalSales        Int                       @default(0)
  viewCount         Int                       @default(0)
  sortOrder         Int                       @default(0)
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt
  partner           Partner                   @relation(fields: [partnerId], references: [id], onDelete: Cascade)
  reviews           PartnerProductReview[]

  @@index([partnerId])
  @@index([type])
  @@index([category])
  @@index([status])
  @@index([isFeatured])
  @@index([isPopular])
  @@index([price])
  @@index([rating])
  @@index([createdAt])
}

model PartnerProductReview {
  id           String         @id @default(cuid())
  productId    String
  userId       String
  rating       Float
  title        String?
  comment      String?
  isPublished  Boolean        @default(true)
  isVerified   Boolean        @default(false)
  helpfulCount Int            @default(0)
  createdAt    DateTime       @default(now())
  updatedAt    DateTime       @updatedAt
  product      PartnerProduct @relation(fields: [productId], references: [id], onDelete: Cascade)
  user         User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([productId, userId])
  @@index([productId])
  @@index([userId])
  @@index([rating])
  @@index([isPublished])
}

model PartnerProductCategory {
  id          String   @id @default(cuid())
  partnerId   String
  name        String
  description String?
  imageUrl    String?
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  partner     Partner  @relation(fields: [partnerId], references: [id], onDelete: Cascade)

  @@unique([partnerId, name])
  @@index([partnerId])
  @@index([isActive])
}
