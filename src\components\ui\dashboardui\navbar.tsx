

"use client"

import { useState, useEffect } from "react"
import { User<PERSON>utton } from "@/components/auth/user-button"
import { But<PERSON> } from "@/components/ui/button"

import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"

import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import {
  Menu,
  X,
  Settings,
  Mail,
  Wallet,
  HelpCircle,
  Calendar,
  Plus,
  ChevronDown,
  LogOut,
  User as UserIcon,
  Home,
  ExternalLink,
  Users,
  CheckCircle2,
  Crown
} from "lucide-react"
import { ThemeToggle } from "@/components/theme-toggle"
import { useCurrentUser } from "@/hooks/use-current-user"
import { useCurrentRole } from "@/hooks/use-current-role"
import { cn } from "@/lib/utils"
import { NotificationBell } from "@/components/ui/notification-bell"

interface NavbarProps {
  onMobileMenuToggle?: () => void;
  isMobileMenuOpen?: boolean;
}

export function Navbar({ onMobileMenuToggle, isMobileMenuOpen = false }: NavbarProps = {}) {
  const pathname = usePathname()
  const router = useRouter()
  const user = useCurrentUser()
  const role = useCurrentRole()

  const formatBalance = (balance: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(balance)
  }

  // Close mobile menu when route changes
  useEffect(() => {
    if (onMobileMenuToggle && isMobileMenuOpen) {
      onMobileMenuToggle()
    }
  }, [pathname, onMobileMenuToggle, isMobileMenuOpen])



  // Quick actions based on user role
  const getQuickActions = () => {
    if (role === 'ORGANIZER') {
      return [
        { label: 'Create Event', href: '/dashboard/organizer/events/createevent', icon: <Calendar className="h-4 w-4" /> },
        { label: 'Manage Team', href: '/dashboard/organizer/team', icon: <Users className="h-4 w-4" /> },
        { label: 'Withdraw Funds', href: '/organizer/wallet/withdraw', icon: <Wallet className="h-4 w-4" /> },
      ]
    } else if (role === 'ADMIN' || role === 'SUPERADMIN') {
      return [
        { label: 'Manage Users', href: '/admin/users', icon: <UserIcon className="h-4 w-4" /> },
        { label: 'Manage Teams', href: '/admin/teams', icon: <Users className="h-4 w-4" /> },
        { label: 'Verifications', href: '/admin/verifications', icon: <CheckCircle2 className="h-4 w-4" /> },
      ]
    } else {
      return [
        { label: 'Browse Events', href: '/events', icon: <Calendar className="h-4 w-4" /> },
        { label: 'My Tickets', href: '/user/tickets', icon: <Wallet className="h-4 w-4" /> },
      ]
    }
  }

  const quickActions = getQuickActions()



  return (
    <header className="z-30 w-full border-b bg-white">
      <div className="flex h-16 items-center justify-between px-4 md:px-6">
        {/* Left section - Mobile menu toggle */}
        <div className="flex items-center">
          <button
            onClick={onMobileMenuToggle}
            className="inline-flex items-center justify-center rounded-md p-2 text-gray-500 hover:bg-gray-100 lg:hidden"
          >
            <span className="sr-only">Open menu</span>
            {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
          </button>
        </div>

        {/* Center section - Empty */}
        <div className="flex-1"></div>

        {/* Right section - Quick actions, notifications, and user menu */}
        <div className="flex items-center gap-3">
          {/* Quick Actions */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="hidden md:flex gap-1">
                <Plus className="h-4 w-4" />
                <span>Quick Actions</span>
                <ChevronDown className="h-3 w-3 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {quickActions.map((action, index) => (
                <DropdownMenuItem key={index} asChild>
                  <Link href={action.href} className="flex w-full cursor-pointer items-center">
                    {action.icon}
                    <span className="ml-2">{action.label}</span>
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Visit Website */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" asChild className="hidden md:flex">
                  <Link href="/" target="_blank">
                    <ExternalLink className="h-5 w-5" />
                    <span className="sr-only">Visit Website</span>
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Visit Website</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Theme Toggle */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    const isDark = document.documentElement.classList.contains('dark');
                    const newTheme = isDark ? "light" : "dark";
                    document.documentElement.classList.remove(isDark ? "dark" : "light");
                    document.documentElement.classList.add(newTheme);
                    // Also update the theme in localStorage via next-themes
                    const themeScript = document.querySelector('[data-next-themes]');
                    if (themeScript) {
                      const event = new CustomEvent('theme-change', { detail: { theme: newTheme } });
                      themeScript.dispatchEvent(event);
                    }
                  }}
                >
                  <ThemeToggle asChild />
                  <span className="sr-only">Toggle Theme</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Toggle Theme</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Help */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" asChild>
                  <Link href="/help">
                    <HelpCircle className="h-5 w-5" />
                    <span className="sr-only">Help</span>
                  </Link>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Help</TooltipContent>
            </Tooltip>
          </TooltipProvider>

          {/* Notifications */}
          <NotificationBell />

          {/* Account Balance */}
          {typeof user?.accountBalance === 'number' && (
            <div className="hidden md:flex items-center gap-2 px-3 py-1.5 bg-green-50 rounded-md">
              <Wallet className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-700">{formatBalance(user.accountBalance)}</span>
            </div>
          )}

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="rounded-full h-8 w-8 overflow-hidden">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.image || undefined} alt={user?.name || 'User'} />
                  <AvatarFallback>{user?.name?.charAt(0) || 'U'}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <div className="flex items-center justify-start gap-2 p-2">
                <div className="flex flex-col space-y-0.5">
                  <p className="text-sm font-medium">{user?.name || 'User'}</p>
                  <p className="text-xs text-gray-500">{user?.email}</p>
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href={
                  role === 'VENDOR' ? '/dashboard/vendor' :
                  role === 'ORGANIZER' ? '/dashboard/organizer' :
                  role === 'ADMIN' || role === 'SUPERADMIN' ? '/dashboard/admin' :
                  role === 'PARTNER' ? '/dashboard/partner' :
                  '/dashboard/user'
                } className="cursor-pointer">
                  <Home className="mr-2 h-4 w-4" />
                  <span>Dashboard</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={
                  role === 'VENDOR' ? '/dashboard/vendor/settings' :
                  role === 'ORGANIZER' ? '/dashboard/organizer/settings' :
                  role === 'ADMIN' || role === 'SUPERADMIN' ? '/dashboard/admin/settings' :
                  role === 'PARTNER' ? '/dashboard/partner/settings' :
                  '/dashboard/user/settings'
                } className="cursor-pointer">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={
                  role === 'VENDOR' ? '/dashboard/vendor/profile' :
                  role === 'ORGANIZER' ? '/dashboard/organizer/profile' :
                  role === 'ADMIN' || role === 'SUPERADMIN' ? '/dashboard/admin/profile' :
                  role === 'PARTNER' ? '/dashboard/partner/profile' :
                  '/dashboard/user/profile'
                } className="cursor-pointer">
                  <UserIcon className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/dashboard/wallet" className="cursor-pointer">
                  <Wallet className="mr-2 h-4 w-4" />
                  <span>Wallet</span>
                </Link>
              </DropdownMenuItem>
              {role === 'USER' && (
                <DropdownMenuItem asChild>
                  <Link href="/dashboard/user/upgrade" className="cursor-pointer text-purple-600 hover:text-purple-700">
                    <Crown className="mr-2 h-4 w-4" />
                    <span>Elite Upgrade</span>
                  </Link>
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/api/auth/direct-signout" className="cursor-pointer text-red-600 hover:text-red-700">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </Link>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>


    </header>
  )
}