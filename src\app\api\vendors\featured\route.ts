import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

// Helper functions for generating realistic product data
function getProductNameByType(businessType: string, index: number): string {
  const normalizedType = businessType.toUpperCase();
  const productNames = {
    'TECHNOLOGY': [
      'Wireless Bluetooth Headphones',
      'Smart Phone Case',
      'Laptop Stand',
      'USB-C Hub'
    ],
    'ELECTRONICS': [
      'LED Smart Bulb',
      'Wireless Charger',
      'Bluetooth Speaker',
      'Power Bank'
    ],
    'FASHION': [
      'Designer T-Shirt',
      'Leather Handbag',
      'Casual Sneakers',
      'Denim Jacket'
    ],
    'BEAUTY': [
      'Moisturizing Face Cream',
      'Lipstick Set',
      'Hair Styling Gel',
      'Perfume Spray'
    ],
    'FOOD': [
      'Organic Honey',
      'Artisan Bread',
      'Fresh Fruit Basket',
      'Gourmet Coffee'
    ],
    'HEALTH': [
      'Vitamin Supplements',
      'Fitness Tracker',
      'Yoga Mat',
      'Protein Powder'
    ],
    'GENERAL': [
      'Premium Product',
      'Quality Item',
      'Best Seller',
      'Featured Product'
    ]
  };

  const names = productNames[normalizedType as keyof typeof productNames] || productNames.GENERAL;
  return names[index % names.length];
}

function getProductImageByType(businessType: string, index: number): string {
  const normalizedType = businessType.toUpperCase();
  const imageUrls = {
    'TECHNOLOGY': [
      'https://images.unsplash.com/photo-*************-5e560c06d30e?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    ],
    'ELECTRONICS': [
      'https://images.unsplash.com/photo-**********-fcd25c85cd64?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1609592806596-4d8b5b1d7d0e?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    ],
    'FASHION': [
      'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    ],
    'BEAUTY': [
      'https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1522335789203-aabd1fc54bc9?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1588159343745-445ae0b16383?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    ],
    'FOOD': [
      'https://images.unsplash.com/photo-**********-9d2a7deb7f62?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1610832958506-aa56368176cf?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-**********-641a0ac8b55e?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    ],
    'HEALTH': [
      'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-**********-0f2fcb009e0b?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1593095948071-474c5cc2989d?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    ],
    'GENERAL': [
      'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-**********-0cfed4f6a45d?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-**********-7eec264c27ff?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    ]
  };

  const images = imageUrls[normalizedType as keyof typeof imageUrls] || imageUrls.GENERAL;
  return images[index % images.length];
}
import { getVendorPlaceholderImage, getProductPlaceholderImage } from '@/lib/image-utils';

/**
 * GET /api/vendors/featured
 * Get featured vendors for homepage display
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '6');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get featured vendors - simplified query
    const [vendors, totalCount] = await Promise.all([
      db.vendorProfile.findMany({
        where: {
          featured: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
          // Note: Products relation will be added when vendor products are implemented
        },
      }),
      db.vendorProfile.count({
        where: {
          featured: true,
        },
      }),
    ]);

    // Transform the data to match the component interface
    const transformedVendors = vendors.map(vendor => ({
      id: vendor.id,
      businessName: vendor.businessName,
      businessType: vendor.businessType || 'Business',
      description: vendor.description,
      city: vendor.city || 'Unknown',
      province: vendor.province || 'Unknown',
      logo: vendor.logo,
      bannerImage: vendor.bannerImage,
      rating: vendor.averageRating || 0,
      totalReviews: vendor.totalReviews,
      isVerified: vendor.verificationStatus === 'APPROVED',
      featured: vendor.featured,
      totalProducts: 0, // Will be updated when vendor products are implemented
      totalSales: 0, // Will be updated when vendor orders are implemented
      contactPhone: vendor.phoneNumber || `+260 ${Math.floor(Math.random() * 900) + 100} ${Math.floor(Math.random() * 900000) + 100000}`,
      website: vendor.website || `https://${vendor.businessName.toLowerCase().replace(/\s+/g, '')}.com`,
      email: vendor.email || `info@${vendor.businessName.toLowerCase().replace(/\s+/g, '')}.com`,
      acceptsNfcPayments: Math.random() > 0.3, // 70% chance of accepting NFC (mock data)
      productCategories: Array.isArray(vendor.productCategories)
        ? vendor.productCategories
        : vendor.productCategories
          ? [vendor.productCategories]
          : [],
      sampleProducts: [], // Only show products if they have real images from database
    }));

    return NextResponse.json({
      vendors: transformedVendors,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: Math.floor(offset / limit) + 1,
        hasNextPage: offset + limit < totalCount,
        hasPrevPage: offset > 0,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error fetching featured vendors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured vendors' },
      { status: 500 }
    );
  }
}
