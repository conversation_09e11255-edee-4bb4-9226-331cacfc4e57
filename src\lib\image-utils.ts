// Image utility functions for featured content

export const getVendorPlaceholderImage = (businessType: string, businessName: string) => {
  const seed = businessName.toLowerCase().replace(/\s+/g, '');
  
  // Use Unsplash for high-quality placeholder images based on business type
  const imageCategories = {
    'ELECTRONICS': 'technology,electronics,gadgets',
    'TECHNOLOGY': 'technology,computer,software',
    'FASHION': 'fashion,clothing,style',
    'FOOD': 'food,restaurant,cuisine',
    'BEAUTY': 'beauty,cosmetics,spa',
    'HEALTH': 'health,medical,wellness',
    'AUTOMOTIVE': 'car,automotive,vehicle',
    'HOME': 'home,interior,furniture',
    'SPORTS': 'sports,fitness,gym',
    'EDUCATION': 'education,books,learning',
    'ENTERTAINMENT': 'entertainment,music,events',
    'BUSINESS': 'business,office,professional',
    'DEFAULT': 'business,store,shop'
  };

  const category = imageCategories[businessType as keyof typeof imageCategories] || imageCategories.DEFAULT;
  
  return {
    banner: `https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=400&fit=crop&crop=center&auto=format&q=80&seed=${seed}`,
    logo: `https://ui-avatars.com/api/?name=${encodeURIComponent(businessName)}&size=128&background=random&color=fff&format=png`
  };
};

export const getPartnerPlaceholderImage = (partnerType: string, businessName: string) => {
  const seed = businessName.toLowerCase().replace(/\s+/g, '');
  
  // Use Unsplash for high-quality placeholder images based on partner type
  const imageCategories = {
    'HOTEL': 'hotel,luxury,accommodation',
    'RESTAURANT': 'restaurant,dining,food',
    'BAR': 'bar,cocktails,nightlife',
    'NIGHTCLUB': 'nightclub,party,entertainment',
    'CAFE': 'cafe,coffee,cozy',
    'LOUNGE': 'lounge,relaxation,comfort',
    'VENUE': 'venue,event,space',
    'CATERING': 'catering,food,service',
    'TRANSPORT': 'transport,travel,vehicle',
    'ENTERTAINMENT': 'entertainment,fun,activity',
    'RETAIL': 'retail,shopping,store',
    'SERVICE': 'service,professional,business',
    'DEFAULT': 'business,service,professional'
  };

  const category = imageCategories[partnerType as keyof typeof imageCategories] || imageCategories.DEFAULT;
  
  return {
    banner: `https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=800&h=400&fit=crop&crop=center&auto=format&q=80&seed=${seed}`,
    logo: `https://ui-avatars.com/api/?name=${encodeURIComponent(businessName)}&size=128&background=random&color=fff&format=png`
  };
};

export const getProductPlaceholderImage = (productName: string, category?: string) => {
  const seed = productName.toLowerCase().replace(/\s+/g, '');
  
  const imageCategories = {
    'electronics': 'technology,electronics,gadgets',
    'fashion': 'fashion,clothing,style',
    'food': 'food,delicious,meal',
    'beauty': 'beauty,cosmetics,skincare',
    'health': 'health,wellness,fitness',
    'home': 'home,decor,furniture',
    'sports': 'sports,equipment,fitness',
    'books': 'books,education,reading',
    'toys': 'toys,games,fun',
    'automotive': 'car,automotive,parts',
    'default': 'product,item,shopping'
  };

  const searchCategory = category?.toLowerCase() || 'default';
  const searchTerms = imageCategories[searchCategory as keyof typeof imageCategories] || imageCategories.default;
  
  return `https://images.unsplash.com/photo-**********-b33ff0c44a43?w=300&h=300&fit=crop&crop=center&auto=format&q=80&seed=${seed}`;
};

export const getServicePlaceholderImage = (serviceName: string, partnerType: string) => {
  const seed = serviceName.toLowerCase().replace(/\s+/g, '');
  
  const serviceImages = {
    'HOTEL': {
      'deluxe room': 'https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'spa package': 'https://images.unsplash.com/photo-**********-4ab6ce6db874?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'default': 'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    },
    'RESTAURANT': {
      'chef special': 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'family meal': 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'default': 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    },
    'BAR': {
      'premium cocktails': 'https://images.unsplash.com/photo-1514362545857-3bc16c4c7d1b?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'happy hour': 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300&h=300&fit=crop&crop=center&auto=format&q=80',
      'default': 'https://images.unsplash.com/photo-1470337458703-46ad1756a187?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    },
    'DEFAULT': {
      'default': 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300&h=300&fit=crop&crop=center&auto=format&q=80'
    }
  };

  const partnerImages = serviceImages[partnerType as keyof typeof serviceImages] || serviceImages.DEFAULT;
  const serviceKey = serviceName.toLowerCase();
  
  return partnerImages[serviceKey as keyof typeof partnerImages] || partnerImages.default || serviceImages.DEFAULT.default;
};

// Generate gradient backgrounds as fallback
export const generateGradientBackground = (seed: string) => {
  const gradients = [
    'from-blue-500 to-purple-600',
    'from-green-500 to-blue-600',
    'from-purple-500 to-pink-600',
    'from-yellow-500 to-red-600',
    'from-indigo-500 to-purple-600',
    'from-pink-500 to-rose-600',
    'from-cyan-500 to-blue-600',
    'from-emerald-500 to-teal-600',
    'from-orange-500 to-red-600',
    'from-violet-500 to-purple-600'
  ];
  
  const hash = seed.split('').reduce((a, b) => {
    a = ((a << 5) - a) + b.charCodeAt(0);
    return a & a;
  }, 0);
  
  return gradients[Math.abs(hash) % gradients.length];
};
