import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

/**
 * GET /api/vendors/[id]
 * Get a specific vendor's public profile with their products
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Await the params Promise
    const resolvedParams = await context.params;
    const vendorId = resolvedParams.id;

    // Validate vendorId
    if (!vendorId) {
      return NextResponse.json(
        { error: 'Vendor ID is required' },
        { status: 400 }
      );
    }

    // Get current date for featuring checks
    const now = new Date();

    // Fetch vendor with their published products and featuring information
    const vendor = await db.vendorProfile.findUnique({
      where: {
        id: vendorId,
        verificationStatus: 'APPROVED', // Only show approved vendors
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            email: true,
          },
        },
        // Include published products
        products: {
          where: {
            status: 'Onsale', // Only published products
            stockQuantity: {
              gt: 0, // Only products with stock
            },
          },
          select: {
            id: true,
            name: true,
            description: true,
            about: true,
            price: true,
            category: true,
            imagePath: true,
            stockQuantity: true,
            weight: true,
            dimensions: true,
            material: true,
            color: true,
            productType: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
        // Include active featuring information
        vendorFeaturings: {
          where: {
            status: 'ACTIVE',
            endDate: {
              gte: now, // Only active featurings
            },
          },
          orderBy: {
            endDate: 'desc',
          },
          take: 1, // Get the latest active featuring
        },
        // Include reviews if available
        reviews: {
          take: 10,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            user: {
              select: {
                name: true,
                image: true,
              },
            },
          },
        },
      },
    });

    if (!vendor) {
      return NextResponse.json(
        { error: 'Vendor not found or not approved' },
        { status: 404 }
      );
    }

    // Check if vendor has expired featuring
    const activeFeaturing = vendor.vendorFeaturings[0] || null;
    const isFeatured = vendor.featured && activeFeaturing !== null;

    // Update vendor if featuring has expired
    if (vendor.featured && !activeFeaturing) {
      await db.vendorProfile.update({
        where: { id: vendor.id },
        data: { featured: false },
      });
    }

    // Add full image URLs
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    
    // Format the vendor data
    const formattedVendor = {
      id: vendor.id,
      businessName: vendor.businessName,
      businessType: vendor.businessType,
      description: vendor.description,
      location: vendor.city && vendor.province 
        ? `${vendor.city}, ${vendor.province}` 
        : vendor.city || vendor.province || null,
      city: vendor.city,
      province: vendor.province,
      physicalAddress: vendor.physicalAddress,
      postalCode: vendor.postalCode,
      phoneNumber: vendor.phoneNumber,
      alternativePhoneNumber: vendor.alternativePhoneNumber,
      email: vendor.email,
      website: vendor.website,
      socialLinks: vendor.socialLinks,
      logo: vendor.logo,
      bannerImage: vendor.bannerImage,
      galleryImages: vendor.galleryImages,
      productCategories: vendor.productCategories,
      serviceCategories: vendor.serviceCategories,
      specializations: vendor.specializations,
      yearEstablished: vendor.yearEstablished,
      employeeCount: vendor.employeeCount,
      businessSize: vendor.businessSize,
      acceptedPaymentMethods: vendor.acceptedPaymentMethods,
      businessHours: vendor.businessHours,
      rating: vendor.averageRating || 0,
      totalReviews: vendor.totalReviews || 0,
      totalSales: vendor.totalSales || 0,
      totalOrders: vendor.totalOrders || 0,
      featured: isFeatured,
      verified: vendor.verificationStatus === 'APPROVED',
      verificationStatus: vendor.verificationStatus,
      createdAt: vendor.createdAt,
      updatedAt: vendor.updatedAt,
      // Add featuring information
      featuring: activeFeaturing ? {
        tier: activeFeaturing.tier,
        endDate: activeFeaturing.endDate.toISOString(),
        badge: activeFeaturing.tier === 'ELITE' ? 'Elite' :
               activeFeaturing.tier === 'PREMIUM' ? 'Premium' : 'Featured',
        badgeColor: activeFeaturing.tier === 'ELITE' ? 'gold' :
                    activeFeaturing.tier === 'PREMIUM' ? 'purple' : 'blue',
      } : null,
      user: {
        id: vendor.user.id,
        name: vendor.user.name,
        image: vendor.user.image,
      },
      products: vendor.products.map(product => ({
        ...product,
        imageUrl: product.imagePath ? `${baseUrl}${product.imagePath}` : null,
      })),
      reviews: vendor.reviews.map(review => ({
        id: review.id,
        rating: review.rating,
        comment: review.comment,
        createdAt: review.createdAt,
        user: {
          name: review.user.name,
          image: review.user.image,
        },
      })),
      // Statistics
      stats: {
        totalProducts: vendor.products.length,
        totalReviews: vendor.totalReviews || 0,
        averageRating: vendor.averageRating || 0,
        totalSales: vendor.totalSales || 0,
        totalOrders: vendor.totalOrders || 0,
      },
    };

    return NextResponse.json(formattedVendor);
  } catch (error) {
    console.error('Error fetching vendor:', error);
    return NextResponse.json(
      { error: 'Failed to fetch vendor' },
      { status: 500 }
    );
  }
}
