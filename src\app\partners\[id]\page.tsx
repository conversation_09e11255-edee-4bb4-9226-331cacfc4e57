"use client";

import { useState, useEffect, use } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import {
  Star,
  MapPin,
  Phone,
  Mail,
  Globe,
  Clock,
  CreditCard,
  Calendar,
  Utensils,
  Coffee,
  Wine,
  Music,
  Bed,
  CheckCircle2,
  ChevronLeft
} from 'lucide-react';
import { PartnerMenuSection } from '@/components/partners/PartnerMenuSection';
import { PartnerProductsSection } from '@/components/partners/PartnerProductsSection';
import { PartnerReviewsSection } from '@/components/partners/PartnerReviewsSection';
import { PartnerPromotionsSection } from '@/components/partners/PartnerPromotionsSection';
import { PartnerNFCPaymentSection } from '@/components/partners/PartnerNFCPaymentSection';

interface Partner {
  id: string;
  businessName: string;
  partnerType: string;
  tier: string;
  description?: string;
  address: string;
  city: string;
  province: string;
  postalCode?: string;
  country: string;
  latitude?: number;
  longitude?: number;
  contactName: string;
  contactEmail: string;
  contactPhone: string;
  website?: string;
  logo?: string;
  bannerImage?: string;
  galleryImages?: any;
  businessHours?: any;
  amenities: string[];
  priceRange?: string;
  rating?: number;
  totalReviews: number;
  isVerified: boolean;
  featured: boolean;
  acceptsNfcPayments: boolean;
  promotions: any[];
  reviews: any[];
  menuItems: any[];
  // products: any[]; // Temporarily disabled until database is updated
  loyaltyProgram?: any;
  eventPartnerships: any[];
}

export default function PartnerDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const [partner, setPartner] = useState<Partner | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const searchParams = useSearchParams();
  const eventId = searchParams?.get('eventId');

  // Use React.use() to unwrap the Promise
  const resolvedParams = use(params);
  const partnerId = resolvedParams.id;

  useEffect(() => {
    const fetchPartner = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/partners/${partnerId}`);

        if (!response.ok) {
          throw new Error('Failed to fetch partner details');
        }

        const data = await response.json();
        setPartner(data);
      } catch (err) {
        console.error('Error fetching partner details:', err);
        setError('Failed to load partner details. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchPartner();
  }, [partnerId]);

  if (loading) {
    return <PartnerDetailSkeleton />;
  }

  if (error || !partner) {
    return (
      <div className="container mx-auto py-12 px-4">
        <div className="text-center py-12 bg-red-50 rounded-lg">
          <h2 className="text-2xl font-bold text-red-700 mb-2">Error</h2>
          <p className="text-red-600">{error || 'Partner not found'}</p>
          <Button asChild className="mt-4">
            <Link href="/partners">
              <ChevronLeft size={16} className="mr-2" />
              Back to Partners
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  // Get partner type icon
  const getPartnerTypeIcon = () => {
    switch (partner.partnerType) {
      case 'HOTEL':
        return <Bed size={20} />;
      case 'RESTAURANT':
        return <Utensils size={20} />;
      case 'BAR':
        return <Wine size={20} />;
      case 'NIGHTCLUB':
        return <Music size={20} />;
      default:
        return <Coffee size={20} />;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Back button */}
      {eventId && (
        <Button variant="ghost" asChild className="mb-4">
          <Link href={`/events/${eventId}`}>
            <ChevronLeft size={16} className="mr-2" />
            Back to Event
          </Link>
        </Button>
      )}

      {/* Header */}
      <div className="relative rounded-lg overflow-hidden mb-8">
        <div className="h-64 bg-gray-200 relative">
          {partner.bannerImage ? (
            <Image
              src={partner.bannerImage}
              alt={partner.businessName}
              fill
              className="object-cover"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gradient-to-r from-blue-500 to-purple-600">
              <h1 className="text-4xl font-bold text-white">{partner.businessName}</h1>
            </div>
          )}
        </div>

        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
          <div className="flex items-center gap-4">
            <div className="h-20 w-20 bg-white rounded-lg overflow-hidden relative">
              {partner.logo ? (
                <Image
                  src={partner.logo}
                  alt={partner.businessName}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-100">
                  <span className="text-3xl font-bold text-gray-400">{partner.businessName.charAt(0)}</span>
                </div>
              )}
            </div>

            <div>
              <div className="flex items-center gap-2">
                <h1 className="text-3xl font-bold text-white">{partner.businessName}</h1>
                {partner.isVerified && (
                  <CheckCircle2 size={20} className="text-blue-400" />
                )}
              </div>

              <div className="flex items-center gap-4 text-white mt-1">
                <Badge className="flex items-center gap-1 bg-white/20">
                  {getPartnerTypeIcon()}
                  <span className="capitalize">{partner.partnerType.toLowerCase()}</span>
                </Badge>

                <div className="flex items-center gap-1">
                  <MapPin size={16} />
                  <span>{partner.city}, {partner.province}</span>
                </div>

                {partner.rating && (
                  <div className="flex items-center gap-1">
                    <Star size={16} className="text-yellow-400 fill-yellow-400" />
                    <span>{partner.rating.toFixed(1)}</span>
                    <span className="text-gray-300">({partner.totalReviews})</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left sidebar */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-start gap-3">
                <MapPin size={20} className="text-gray-500 mt-1" />
                <div>
                  <p className="font-medium">Address</p>
                  <p className="text-gray-600">{partner.address}</p>
                  <p className="text-gray-600">{partner.city}, {partner.province} {partner.postalCode}</p>
                  <p className="text-gray-600">{partner.country}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Phone size={20} className="text-gray-500" />
                <div>
                  <p className="font-medium">Phone</p>
                  <a href={`tel:${partner.contactPhone}`} className="text-blue-600 hover:underline">
                    {partner.contactPhone}
                  </a>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Mail size={20} className="text-gray-500" />
                <div>
                  <p className="font-medium">Email</p>
                  <a href={`mailto:${partner.contactEmail}`} className="text-blue-600 hover:underline">
                    {partner.contactEmail}
                  </a>
                </div>
              </div>

              {partner.website && (
                <div className="flex items-center gap-3">
                  <Globe size={20} className="text-gray-500" />
                  <div>
                    <p className="font-medium">Website</p>
                    <a href={partner.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {partner.website.replace(/^https?:\/\//, '')}
                    </a>
                  </div>
                </div>
              )}

              {partner.businessHours && (
                <div className="flex items-start gap-3">
                  <Clock size={20} className="text-gray-500 mt-1" />
                  <div>
                    <p className="font-medium">Business Hours</p>
                    {Object.entries(partner.businessHours).map(([day, hours]: [string, any]) => (
                      <div key={day} className="flex justify-between text-sm">
                        <span className="capitalize">{day.toLowerCase()}</span>
                        <span className="text-gray-600">{hours.open} - {hours.close}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {partner.amenities.length > 0 && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Amenities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {partner.amenities.map((amenity, index) => (
                    <Badge key={index} variant="outline" className="capitalize">
                      {amenity.toLowerCase()}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {partner.acceptsNfcPayments && (
            <Card className="mt-6 bg-blue-50 border-blue-200">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <CreditCard size={20} />
                  <span>NFC Payments Accepted</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-blue-600 text-sm">
                  This partner accepts contactless NFC payments through the event platform.
                  Use your event app to make secure payments.
                </p>
                <Button className="w-full mt-4" variant="outline">
                  Learn More About NFC Payments
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Main content area */}
        <div className="lg:col-span-2">
          <Tabs defaultValue="about">
            <TabsList className="mb-6">
              <TabsTrigger value="about">About</TabsTrigger>
              {/* Products tab temporarily disabled until database is updated */}
              {/* {partner.products && partner.products.length > 0 && (
                <TabsTrigger value="products">Products & Services</TabsTrigger>
              )} */}
              {partner.menuItems.length > 0 && (
                <TabsTrigger value="menu">Menu</TabsTrigger>
              )}
              <TabsTrigger value="reviews">Reviews</TabsTrigger>
              {partner.promotions.length > 0 && (
                <TabsTrigger value="promotions">Promotions</TabsTrigger>
              )}
              {partner.acceptsNfcPayments && (
                <TabsTrigger value="nfc-payment">NFC Payment</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="about" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>About {partner.businessName}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 whitespace-pre-line">
                    {partner.description || `${partner.businessName} is a ${partner.partnerType.toLowerCase()} located in ${partner.city}, ${partner.province}.`}
                  </p>

                  {partner.galleryImages && (
                    <div className="mt-6">
                      <h3 className="font-semibold text-lg mb-3">Gallery</h3>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {Object.values(partner.galleryImages).map((image: any, index: number) => (
                          <div key={index} className="relative h-32 rounded-md overflow-hidden">
                            <Image
                              src={image}
                              alt={`${partner.businessName} gallery image ${index + 1}`}
                              fill
                              className="object-cover"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {partner.eventPartnerships.length > 0 && (
                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Upcoming Events</CardTitle>
                    <CardDescription>
                      Events where {partner.businessName} is an official partner
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {partner.eventPartnerships.map((partnership) => (
                        <div key={partnership.id} className="flex items-center gap-4">
                          <div className="h-16 w-16 relative rounded-md overflow-hidden bg-gray-100">
                            {partnership.event.imagePath ? (
                              <Image
                                src={partnership.event.imagePath}
                                alt={partnership.event.title}
                                fill
                                className="object-cover"
                              />
                            ) : (
                              <Calendar size={24} className="absolute inset-0 m-auto text-gray-400" />
                            )}
                          </div>
                          <div>
                            <h3 className="font-medium">
                              <Link href={`/events/${partnership.event.id}`} className="hover:text-blue-600">
                                {partnership.event.title}
                              </Link>
                            </h3>
                            <p className="text-sm text-gray-500">
                              {new Date(partnership.event.startDate).toLocaleDateString()}
                            </p>
                            <Badge variant="outline" className="mt-1 capitalize">
                              {partnership.partnerType.toLowerCase().replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Products tab temporarily disabled until database is updated */}
            {/* {partner.products && partner.products.length > 0 && (
              <TabsContent value="products" className="mt-0">
                <PartnerProductsSection
                  partnerId={partner.id}
                  partnerName={partner.businessName}
                  products={partner.products}
                />
              </TabsContent>
            )} */}

            {partner.menuItems.length > 0 && (
              <TabsContent value="menu" className="mt-0">
                <PartnerMenuSection partnerId={partner.id} />
              </TabsContent>
            )}

            <TabsContent value="reviews" className="mt-0">
              <PartnerReviewsSection partnerId={partner.id} />
            </TabsContent>

            {partner.promotions.length > 0 && (
              <TabsContent value="promotions" className="mt-0">
                <PartnerPromotionsSection partnerId={partner.id} />
              </TabsContent>
            )}

            {partner.acceptsNfcPayments && (
              <TabsContent value="nfc-payment" className="mt-0">
                <PartnerNFCPaymentSection partnerId={partner.id} />
              </TabsContent>
            )}
          </Tabs>
        </div>
      </div>
    </div>
  );
}

function PartnerDetailSkeleton() {
  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header skeleton */}
      <div className="relative rounded-lg overflow-hidden mb-8">
        <Skeleton className="h-64 w-full" />
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-20 w-20 rounded-lg" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <div className="flex gap-2">
                <Skeleton className="h-6 w-24" />
                <Skeleton className="h-6 w-32" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent className="space-y-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="flex items-start gap-3">
                  <Skeleton className="h-5 w-5" />
                  <div className="flex-1">
                    <Skeleton className="h-5 w-24 mb-1" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-2">
          <Skeleton className="h-10 w-full mb-6" />
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-3/4" />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
