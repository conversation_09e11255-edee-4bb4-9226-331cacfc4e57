
import { User } from "next-auth";

/**
 * An array of routes that are accessible to the public.
 * These routes do not require authentication.
 * @type {string[]}
 */
// Add these new routes to your publicRoutes array
export const publicRoutes = [
  '/',
  '/auth/new-verification',
  '/auth/welcome',  // Add welcome page as public route
  "/api/getevents?",
  "/api/events/published",
  "/api/events/popular",
  "/api/products/published",  // Add public products endpoint
  "/api/vendors/published",   // Add public vendors endpoint
  "/api/vendors/:id*",        // Add public individual vendor endpoint
  "/vendors",
  "/vendors/:id*",            // Add public vendor profile pages
  "/events",
  "/partners",
  "/partners/:id*",
  "/allevents",
  "/about",
  "/blog",
  '/events/[id]',
  '/api/eventdetails/[id]',
  '/api/eventdetails',  // Add the base path
  '/api/public/events/:id*',   // Add this in case you're using this endpoint
  '/api/public/events/[id]',   // Add this in case you're using this endpoint
  '/api/payment/:id*',   // Add this in case you're using this endpoint
  '/api/partners/:id*',
  '/api/partners',
  '/api/events/:id*/partners',
  '/api/events/:id*/partners/:partnerId*',
  '/api/payment/[id]',   // Add this in case you're using this endpoint
  '/api/events',        // Add the base path for events
  '/events/:id*',  // Make sure the event details page itself is public
  '/events/[id]',  // Alternative format
  '/events/:category*/:title*/:id*',  // New SEO-friendly format
  '/events/[category]/[title]/[id]',  // Alternative format for new structure
  '/buy-ticket/:id*',  // If you want the ticket purchase page to be public too

  // API documentation routes
  '/api/docs',
  '/api/docs/:path*',
];

/**
 * An array of routes that are used for authentication.
 * @type {string[]}
 */
export const authRoutes = [
  '/auth/login',
  '/auth/register',
  '/auth/error',
  '/auth/reset',
  '/auth/new-password',
  '/auth/select-role',
  '/auth/partner-login',
  '/auth/partner-register',
];

/**
 * The prefix for authentication routes.
 * Routes that start with this prefix are used for API authentication purposes.
 * @type {string}
 */
export const apiAuthPrefix = '/api/auth';

/**
 * The default redirect path after logging in.
 * This is used by NextAuth when it needs a string value.
 * Users should be redirected to role-specific dashboards, but this serves as a fallback.
 * @type {string}
 */
export const DEFAULT_LOGIN_REDIRECT = '/auth/select-role';

/**
 * A function that returns the appropriate redirect URL based on the user's role.
 * @param {User} user - The authenticated user object
 * @returns {string} The redirect URL
 */
export const getRedirectUrlByRole = (user: User & { role?: string }): string => {
  switch (user.role) {
    case 'ADMIN':
      return '/admin/dashboard';
    case 'USER':
      return '/dashboard/user';
    case 'ORGANIZER':
      return '/dashboard/organizer';
    case 'VENDOR':
      return '/dashboard/vendor';
    case 'PARTNER':
      return '/dashboard/partner';
    case 'SUPERADMIN':
      return '/admin/dashboard';
    case 'DEVELOPER':
      return '/dashboard/developer';
    default:
      return DEFAULT_LOGIN_REDIRECT; // Fallback to role selection if role is not recognized
  }
};
