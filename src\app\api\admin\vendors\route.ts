import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/vendors
 * Get all vendors for admin dashboard
 */
export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');
    const status = searchParams.get('status');
    const featured = searchParams.get('featured');

    // Build where clause
    const where: any = {};
    
    if (status && status !== 'all') {
      where.verificationStatus = status;
    }
    
    if (featured === 'true') {
      where.featured = true;
    } else if (featured === 'false') {
      where.featured = false;
    }

    const [vendors, totalCount] = await Promise.all([
      db.vendorProfile.findMany({
        where,
        orderBy: [
          { featured: 'desc' },
          { createdAt: 'desc' }
        ],
        take: limit,
        skip: offset,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              products: true,
              reviews: true,
            },
          },
        },
      }),
      db.vendorProfile.count({ where }),
    ]);

    // Transform the data to match the expected format
    const transformedVendors = vendors.map(vendor => ({
      id: vendor.id,
      businessName: vendor.businessName,
      businessType: vendor.businessType,
      description: vendor.description,
      city: vendor.city,
      province: vendor.province,
      logo: vendor.logo,
      bannerImage: vendor.bannerImage,
      rating: vendor.averageRating,
      totalReviews: vendor._count.reviews,
      featured: vendor.featured,
      verificationStatus: vendor.verificationStatus,
      totalProducts: vendor._count.products,
      totalSales: 0, // This would need to be calculated from actual sales data
      user: vendor.user,
    }));

    return NextResponse.json({
      vendors: transformedVendors,
      totalCount,
      hasMore: offset + limit < totalCount,
    });
  } catch (error) {
    console.error('Error fetching vendors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch vendors' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/vendors
 * Update vendor status or featured status
 */
export async function PATCH(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { vendorId, updates } = body;

    if (!vendorId || !updates) {
      return NextResponse.json(
        { error: 'Missing required fields: vendorId and updates' },
        { status: 400 }
      );
    }

    // Update the vendor
    const updatedVendor = await db.vendorProfile.update({
      where: { id: vendorId },
      data: updates,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: 'Vendor updated successfully',
      vendor: updatedVendor,
    });
  } catch (error) {
    console.error('Error updating vendor:', error);
    return NextResponse.json(
      { error: 'Failed to update vendor' },
      { status: 500 }
    );
  }
}
