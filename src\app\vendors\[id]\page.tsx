'use client';

import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Star, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  BadgeCheck, 
  Package, 
  Store,
  ArrowLeft,
  Clock,
  Users,
  ShoppingCart,
  Loader2
} from 'lucide-react';
import Image from 'next/image';
import AppLayout from '@/components/ui/layout';

interface VendorProfile {
  id: string;
  businessName: string;
  businessType?: string;
  description?: string;
  location?: string;
  city?: string;
  province?: string;
  physicalAddress?: string;
  phoneNumber?: string;
  email?: string;
  website?: string;
  logo?: string;
  bannerImage?: string;
  productCategories?: string;
  serviceCategories?: string;
  specializations?: string;
  yearEstablished?: number;
  businessHours?: any;
  rating: number;
  totalReviews: number;
  totalSales: number;
  totalOrders: number;
  featured: boolean;
  verified: boolean;
  featuring?: {
    tier: string;
    badge: string;
    badgeColor: string;
  } | null;
  user: {
    id: string;
    name: string;
    image?: string;
  };
  products: Array<{
    id: string;
    name: string;
    description?: string;
    price: number;
    category: string;
    imagePath?: string;
    imageUrl?: string;
    stockQuantity: number;
  }>;
  reviews: Array<{
    id: string;
    rating: number;
    comment?: string;
    createdAt: string;
    user: {
      name: string;
      image?: string;
    };
  }>;
  stats: {
    totalProducts: number;
    totalReviews: number;
    averageRating: number;
    totalSales: number;
    totalOrders: number;
  };
}

export default function VendorProfilePage() {
  const params = useParams();
  const router = useRouter();
  const [vendor, setVendor] = useState<VendorProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const vendorId = params?.id as string;

  useEffect(() => {
    const fetchVendor = async () => {
      if (!vendorId) return;

      try {
        setLoading(true);
        const response = await fetch(`/api/vendors/${vendorId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('Vendor not found');
          } else {
            setError('Failed to load vendor profile');
          }
          return;
        }
        
        const data = await response.json();
        setVendor(data);
      } catch (err) {
        console.error('Error fetching vendor:', err);
        setError('Failed to load vendor profile');
      } finally {
        setLoading(false);
      }
    };

    fetchVendor();
  }, [vendorId]);

  if (loading) {
    return (
      <AppLayout>
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading vendor profile...</span>
        </div>
      </AppLayout>
    );
  }

  if (error || !vendor) {
    return (
      <AppLayout>
        <div className="container mx-auto py-8 px-4">
          <Card className="max-w-md mx-auto">
            <CardHeader>
              <CardTitle className="text-center text-red-500">
                {error || 'Vendor Not Found'}
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-muted-foreground mb-4">
                {error === 'Vendor not found' 
                  ? 'The vendor you are looking for does not exist or is not available.'
                  : 'There was an error loading the vendor profile. Please try again later.'
                }
              </p>
              <Button onClick={() => router.push('/vendors')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Vendors
              </Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto py-8 px-4">
        {/* Back Button */}
        <Button 
          variant="outline" 
          onClick={() => router.push('/vendors')}
          className="mb-6"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Vendors
        </Button>

        {/* Vendor Header */}
        <Card className="mb-8">
          {/* Banner */}
          <div className="relative h-48 bg-gradient-to-r from-blue-500 to-purple-600 rounded-t-lg">
            {vendor.bannerImage ? (
              <Image
                src={vendor.bannerImage}
                alt={`${vendor.businessName} banner`}
                fill
                className="object-cover rounded-t-lg"
              />
            ) : (
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-t-lg flex items-center justify-center">
                <div className="text-center text-white">
                  <Store className="h-16 w-16 mx-auto mb-2 opacity-50" />
                  <p className="text-lg font-medium opacity-75">{vendor.businessName}</p>
                </div>
              </div>
            )}
            <div className="absolute inset-0 bg-black/20 rounded-t-lg" />
            {vendor.featured && vendor.featuring && (
              <Badge className={`absolute top-4 right-4 ${
                vendor.featuring.badgeColor === 'gold' ? 'bg-yellow-500' :
                vendor.featuring.badgeColor === 'purple' ? 'bg-purple-500' : 'bg-blue-500'
              }`}>
                <Star className="h-3 w-3 mr-1" />
                {vendor.featuring.badge}
              </Badge>
            )}
          </div>

          {/* Profile Info */}
          <CardContent className="pt-0">
            {/* Avatar */}
            <div className="relative -mt-12 mb-6 flex justify-center">
              <div className="w-24 h-24 rounded-full border-4 border-white bg-white overflow-hidden">
                {vendor.logo || vendor.user.image ? (
                  <Image
                    src={vendor.logo || vendor.user.image || ''}
                    alt={vendor.businessName}
                    width={96}
                    height={96}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                    <Store className="h-12 w-12 text-gray-400" />
                  </div>
                )}
              </div>
            </div>

            {/* Business Info */}
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold flex items-center justify-center gap-2 mb-2">
                {vendor.businessName}
                {vendor.verified && (
                  <BadgeCheck className="h-6 w-6 text-blue-500" />
                )}
              </h1>
              
              {vendor.businessType && (
                <Badge variant="outline" className="mb-2">
                  {vendor.businessType}
                </Badge>
              )}

              {vendor.location && (
                <div className="flex items-center justify-center text-muted-foreground mb-2">
                  <MapPin className="h-4 w-4 mr-1" />
                  {vendor.location}
                </div>
              )}

              {vendor.description && (
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  {vendor.description}
                </p>
              )}
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{vendor.stats.totalProducts}</div>
                <div className="text-sm text-muted-foreground">Products</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{vendor.stats.totalOrders}</div>
                <div className="text-sm text-muted-foreground">Orders</div>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center gap-1">
                  <span className="text-2xl font-bold text-primary">{vendor.rating.toFixed(1)}</span>
                  <Star className="h-5 w-5 text-yellow-500 fill-yellow-500" />
                </div>
                <div className="text-sm text-muted-foreground">Rating</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">{vendor.stats.totalReviews}</div>
                <div className="text-sm text-muted-foreground">Reviews</div>
              </div>
            </div>

            {/* Contact Info */}
            <div className="flex flex-wrap justify-center gap-4">
              {vendor.phoneNumber && (
                <Button variant="outline" size="sm">
                  <Phone className="h-4 w-4 mr-2" />
                  {vendor.phoneNumber}
                </Button>
              )}
              {vendor.email && (
                <Button variant="outline" size="sm">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact
                </Button>
              )}
              {vendor.website && (
                <Button variant="outline" size="sm" asChild>
                  <a href={vendor.website} target="_blank" rel="noopener noreferrer">
                    <Globe className="h-4 w-4 mr-2" />
                    Website
                  </a>
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Products Section */}
        {vendor.products.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Products ({vendor.products.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {vendor.products.map((product) => (
                  <Card key={product.id} className="overflow-hidden">
                    <div className="relative h-48 bg-gray-100">
                      {product.imageUrl || product.imagePath ? (
                        <Image
                          src={product.imageUrl || product.imagePath || ''}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <Package className="h-12 w-12 text-gray-400" />
                        </div>
                      )}
                      <Badge className="absolute top-2 right-2">
                        {product.category.replace(/_/g, ' ')}
                      </Badge>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold mb-2">{product.name}</h3>
                      {product.description && (
                        <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                          {product.description}
                        </p>
                      )}
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-bold text-primary">
                          ZMW {product.price.toFixed(2)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          Stock: {product.stockQuantity}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Reviews Section */}
        {vendor.reviews.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Customer Reviews ({vendor.reviews.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {vendor.reviews.map((review) => (
                  <div key={review.id} className="border-b pb-4 last:border-b-0">
                    <div className="flex items-start gap-3">
                      <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden">
                        {review.user.image ? (
                          <Image
                            src={review.user.image}
                            alt={review.user.name}
                            width={40}
                            height={40}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <Users className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{review.user.name}</span>
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating
                                    ? 'text-yellow-500 fill-yellow-500'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm text-muted-foreground">
                            {new Date(review.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                        {review.comment && (
                          <p className="text-muted-foreground">{review.comment}</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
