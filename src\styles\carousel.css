/* Featured Carousel Styles */
.embla {
  overflow: hidden;
  margin-left: -1rem;
  margin-right: -1rem;
}

.embla__container {
  display: flex;
  gap: 1.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
}

.embla__slide {
  flex: 0 0 auto;
  min-width: 0;
  width: 400px;
  max-width: 400px;
}

/* Smooth scrolling */
.embla__container {
  transition: transform 0.3s ease-in-out;
}

/* Hide scrollbar */
.embla::-webkit-scrollbar {
  display: none;
}

.embla {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Card hover effects */
.featured-card {
  transition: all 0.3s ease;
}

.featured-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Product/Service hover effects */
.product-card {
  transition: all 0.2s ease;
}

.product-card:hover {
  transform: scale(1.02);
}

/* Navigation button styles */
.carousel-nav-btn {
  transition: all 0.3s ease;
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.carousel-nav-btn:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.1);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Positioned navigation arrows */
.carousel-nav-left {
  left: -20px;
  transform: translateY(-50%);
}

.carousel-nav-right {
  right: -20px;
  transform: translateY(-50%);
}

/* Navigation arrow hover effects */
.carousel-nav-left:hover {
  left: -24px;
}

.carousel-nav-right:hover {
  right: -24px;
}

/* Mobile navigation enhancements */
.mobile-nav-hint {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
  backdrop-filter: blur(8px);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* Gradient fade adjustments for navigation */
.nav-fade-left {
  background: linear-gradient(to right, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.nav-fade-right {
  background: linear-gradient(to left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.nav-fade-left-purple {
  background: linear-gradient(to right, rgba(250, 245, 255, 1) 0%, rgba(250, 245, 255, 0) 100%);
}

.nav-fade-right-purple {
  background: linear-gradient(to left, rgba(250, 245, 255, 1) 0%, rgba(250, 245, 255, 0) 100%);
}

/* Scroll indicators */
@keyframes bounce-horizontal {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(4px); }
}

.scroll-indicator {
  animation: bounce-horizontal 2s ease-in-out infinite;
}

/* Navigation button pulse effect */
@keyframes nav-pulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
  50% { box-shadow: 0 0 0 8px rgba(59, 130, 246, 0); }
}

.nav-pulse {
  animation: nav-pulse 2s infinite;
}

/* Enhanced hover states */
.carousel-nav-btn:hover {
  animation: none;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .embla__container {
    gap: 1rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .embla__slide {
    width: 360px;
    max-width: 360px;
  }

  .embla {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
}

@media (max-width: 640px) {
  .embla__slide {
    width: 320px;
    max-width: 320px;
  }

  .embla__container {
    gap: 0.75rem;
  }
}

/* Ensure cards don't get cut off */
.carousel-container {
  padding-left: 1rem;
  padding-right: 1rem;
  margin-left: -1rem;
  margin-right: -1rem;
}

@media (max-width: 768px) {
  .carousel-container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
}

/* Scroll hint animation */
@keyframes scrollHint {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(4px); }
}

.scroll-hint {
  animation: scrollHint 2s ease-in-out infinite;
}

/* Fade gradients for scroll indication */
.fade-right {
  background: linear-gradient(to left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%);
}

.fade-right-purple {
  background: linear-gradient(to left, rgba(250, 245, 255, 1) 0%, rgba(250, 245, 255, 0) 100%);
}

/* Platform Stats Animations */
@keyframes countUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4); }
  50% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.stat-card {
  animation: countUp 0.6s ease-out forwards;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
.stat-card:nth-child(5) { animation-delay: 0.5s; }
.stat-card:nth-child(6) { animation-delay: 0.6s; }
.stat-card:nth-child(7) { animation-delay: 0.7s; }
.stat-card:nth-child(8) { animation-delay: 0.8s; }

.stat-icon {
  animation: float 3s ease-in-out infinite;
}

.stat-value {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Hover effects for stats */
.stat-card:hover .stat-icon {
  animation: pulse-glow 1.5s infinite;
}

/* Responsive adjustments for stats */
@media (max-width: 768px) {
  .stat-card {
    animation-delay: 0s;
  }
}
