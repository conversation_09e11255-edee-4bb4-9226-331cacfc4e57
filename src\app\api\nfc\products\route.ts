import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Create a direct instance for this API route
const prisma = new PrismaClient();

/**
 * GET /api/nfc/products
 * Get all active NFC products with pricing
 */
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url);
    const currency = url.searchParams.get('currency') || 'USD';

    // Get all active NFC products
    const products = await prisma.nFCProductPricing.findMany({
      where: {
        isActive: true,
        currency: currency
      },
      orderBy: {
        price: 'asc'
      }
    });

    // Transform the data for the frontend
    const formattedProducts = products.map((product: any) => {
      return {
        id: product.id,
        deviceType: product.deviceType,
        name: product.name,
        description: product.description,
        price: parseFloat(product.price.toString()),
        currency: product.currency,
        imageUrl: product.imageUrl || getDefaultImageUrl(product.deviceType)
      };
    });

    return NextResponse.json(formattedProducts);

  } catch (error) {
    console.error('Error fetching NFC products:', error);
    return NextResponse.json({
      error: 'Failed to fetch NFC products'
    }, { status: 500 });
  }
}

// Helper function to get default image URL based on device type
function getDefaultImageUrl(deviceType: string): string {
  switch (deviceType) {
    case 'CARD':
      return '/images/nfc-card.jpg';
    case 'FABRIC_WRISTBAND':
      return '/images/nfc-fabric-wristband.jpg';
    case 'PAPER_WRISTBAND':
      return '/images/nfc-paper-wristband.jpg';
    case 'SILICONE_WRISTBAND':
      return '/images/nfc-silicone-wristband.jpg';
    case 'TAG':
      return '/images/nfc-tag.jpg';
    default:
      return '/images/placeholder.jpg';
  }
}
