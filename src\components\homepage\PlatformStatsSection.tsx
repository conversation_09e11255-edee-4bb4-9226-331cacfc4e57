'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  CalendarDays,
  ShoppingCart,
  Handshake,
  User<PERSON>heck,
  TicketCheck,
  Package2,
  Activity,
  Award,
  Sparkles,
  Heart,
  Target,
  Zap
} from 'lucide-react';

interface PlatformStats {
  totalEvents: number;
  totalVendors: number;
  totalPartners: number;
  totalUsers: number;
  totalTicketsSold: number;
  totalProductsSold: number;
  averageRating: number;
  activeEvents: number;
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
};

export default function PlatformStatsSection() {
  const [stats, setStats] = useState<PlatformStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/platform/stats');
        
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        } else {
          // Fallback to mock data if API doesn't exist yet
          setStats({
            totalEvents: 1250,
            totalVendors: 340,
            totalPartners: 180,
            totalUsers: 15600,
            totalTicketsSold: 45200,
            totalProductsSold: 12800,
            averageRating: 4.7,
            activeEvents: 89,
          });
        }
      } catch (error) {
        console.error('Error fetching platform stats:', error);
        // Fallback to mock data
        setStats({
          totalEvents: 1250,
          totalVendors: 340,
          totalPartners: 180,
          totalUsers: 15600,
          totalTicketsSold: 45200,
          totalProductsSold: 12800,
          averageRating: 4.7,
          activeEvents: 89,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Skeleton className="h-8 w-64 mx-auto mb-4" />
            <Skeleton className="h-4 w-96 mx-auto" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <Card key={index}>
                <CardContent className="p-6 text-center">
                  <Skeleton className="h-8 w-8 mx-auto mb-4" />
                  <Skeleton className="h-8 w-16 mx-auto mb-2" />
                  <Skeleton className="h-4 w-20 mx-auto" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (!stats) {
    return null;
  }

  const statItems = [
    {
      icon: CalendarDays,
      value: formatNumber(stats.totalEvents),
      label: 'Amazing Events',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      gradient: 'from-blue-500 to-blue-600',
      description: 'Unforgettable experiences'
    },
    {
      icon: ShoppingCart,
      value: formatNumber(stats.totalVendors),
      label: 'Trusted Vendors',
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      gradient: 'from-emerald-500 to-emerald-600',
      description: 'Quality businesses'
    },
    {
      icon: Handshake,
      value: formatNumber(stats.totalPartners),
      label: 'Premium Partners',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      gradient: 'from-purple-500 to-purple-600',
      description: 'Exclusive partnerships'
    },
    {
      icon: Heart,
      value: formatNumber(stats.totalUsers),
      label: 'Happy Community',
      color: 'text-rose-600',
      bgColor: 'bg-rose-50',
      gradient: 'from-rose-500 to-rose-600',
      description: 'Growing together'
    },
    {
      icon: TicketCheck,
      value: formatNumber(stats.totalTicketsSold),
      label: 'Tickets Booked',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      gradient: 'from-indigo-500 to-indigo-600',
      description: 'Dreams fulfilled'
    },
    {
      icon: Package2,
      value: formatNumber(stats.totalProductsSold),
      label: 'Products Delivered',
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      gradient: 'from-amber-500 to-amber-600',
      description: 'Quality guaranteed'
    },
    {
      icon: Activity,
      value: formatNumber(stats.activeEvents),
      label: 'Live Events',
      color: 'text-teal-600',
      bgColor: 'bg-teal-50',
      gradient: 'from-teal-500 to-teal-600',
      description: 'Happening now'
    },
    {
      icon: Award,
      value: stats.averageRating.toFixed(1),
      label: 'Excellence Rating',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      gradient: 'from-yellow-500 to-yellow-600',
      description: 'Customer satisfaction'
    },
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="flex items-center gap-1">
              <Heart className="h-6 w-6 text-red-500 animate-pulse" />
              <Sparkles className="h-5 w-5 text-yellow-500" />
            </div>
            <h2 className="text-4xl font-black text-gray-900 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Trusted by Thousands
            </h2>
            <div className="flex items-center gap-1">
              <Sparkles className="h-5 w-5 text-yellow-500" />
              <Target className="h-6 w-6 text-green-500" />
            </div>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Join our <span className="font-semibold text-blue-600">thriving community</span> of event organizers, vendors, and partners creating
            <span className="font-semibold text-purple-600"> amazing experiences</span> together.
          </p>
          <div className="flex items-center justify-center gap-2 mt-4 text-sm text-gray-500">
            <Zap className="h-4 w-4 text-yellow-500" />
            <span>Real-time stats • Updated every minute</span>
            <Zap className="h-4 w-4 text-yellow-500" />
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {statItems.map((item, index) => (
            <Card key={index} className="group hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border-0 shadow-lg bg-white overflow-hidden relative">
              {/* Gradient Background */}
              <div className={`absolute inset-0 bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />

              <CardContent className="p-6 text-center relative z-10">
                {/* Icon with enhanced styling */}
                <div className="relative mb-6">
                  <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${item.bgColor} mb-2 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <item.icon className={`h-8 w-8 ${item.color} group-hover:scale-110 transition-transform duration-300`} />
                  </div>
                  {/* Sparkle effect */}
                  <div className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <Sparkles className="h-4 w-4 text-yellow-400" />
                  </div>
                </div>

                {/* Value with enhanced typography */}
                <div className="text-4xl font-black text-gray-900 mb-2 group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
                  {item.value}
                </div>

                {/* Label */}
                <div className="text-sm font-bold text-gray-700 mb-1 uppercase tracking-wide">
                  {item.label}
                </div>

                {/* Description */}
                <div className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {item.description}
                </div>

                {/* Progress bar effect */}
                <div className="mt-4 h-1 bg-gray-100 rounded-full overflow-hidden">
                  <div
                    className={`h-full bg-gradient-to-r ${item.gradient} transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out`}
                    style={{ transitionDelay: `${index * 100}ms` }}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-8 border border-blue-100">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Sparkles className="h-6 w-6 text-yellow-500" />
              <h3 className="text-2xl font-bold text-gray-900">Ready to be part of something amazing?</h3>
              <Sparkles className="h-6 w-6 text-yellow-500" />
            </div>
            <p className="text-gray-600 mb-8 text-lg">
              Join thousands of successful businesses and create unforgettable experiences
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/auth/register?role=organizer"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <CalendarDays className="h-5 w-5 mr-2" />
              Create Events
            </a>
            <a
              href="/auth/register?role=vendor"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 transition-colors"
            >
              <ShoppingCart className="h-5 w-5 mr-2" />
              Become a Vendor
            </a>
            <a
              href="/auth/register?role=partner"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 transition-colors"
            >
              <Handshake className="h-5 w-5 mr-2" />
              Join as Partner
            </a>
          </div>
          </div>
        </div>
      </div>
    </section>
  );
}
