import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

/**
 * GET /api/partners/featured
 * Get featured partners for public display (no auth required)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '6');
    const offset = parseInt(searchParams.get('offset') || '0');

    const [partners, totalCount] = await Promise.all([
      db.partner.findMany({
        where: { 
          featured: true,
          isVerified: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          promotions: {
            where: {
              isActive: true,
              startDate: { lte: new Date() },
              endDate: { gte: new Date() },
            },
            take: 4, // Get more promotions to use as services
            select: {
              id: true,
              title: true,
              description: true,
              discountValue: true,
              discountType: true,
              imageUrl: true,
            },
          },
          _count: {
            select: {
              // products: true, // Will be enabled when partner products are ready
              reviews: true,
              promotions: {
                where: {
                  isActive: true,
                },
              },
            },
          },
        },
      }),
      db.partner.count({ 
        where: { 
          featured: true,
          isVerified: true,
        },
      }),
    ]);

    // Transform the data to match the component interface
    const transformedPartners = partners.map(partner => ({
      id: partner.id,
      businessName: partner.businessName,
      partnerType: partner.partnerType,
      tier: partner.tier,
      description: partner.description,
      city: partner.city,
      province: partner.province,
      logo: partner.logo,
      bannerImage: partner.bannerImage,
      rating: partner.rating,
      totalReviews: partner.totalReviews,
      isVerified: partner.isVerified,
      featured: partner.featured,
      acceptsNfcPayments: partner.acceptsNfcPayments,
      website: partner.website,
      contactPhone: partner.contactPhone,
      email: partner.contactEmail || `contact@${partner.businessName.toLowerCase().replace(/\s+/g, '')}.com`,
      _count: {
        products: 0, // Will be updated when partner products are implemented
        reviews: partner._count.reviews,
        promotions: partner._count.promotions,
      },
      currentPromotions: partner.promotions.map(promo => ({
        id: promo.id,
        title: promo.title,
        discountValue: promo.discountValue,
        discountType: promo.discountType,
      })),
      sampleServices: partner.promotions
        .filter(promo => promo.imageUrl) // Only include promotions with real images
        .slice(0, 2)
        .map((promo) => {
          // Generate base price for the service
          const basePrice = partner.partnerType === 'HOTEL' ? Math.floor(Math.random() * 500) + 200 :
                           partner.partnerType === 'RESTAURANT' ? Math.floor(Math.random() * 200) + 100 :
                           partner.partnerType === 'BAR' ? Math.floor(Math.random() * 120) + 50 :
                           partner.partnerType === 'NIGHTCLUB' ? Math.floor(Math.random() * 200) + 80 :
                           partner.partnerType === 'CAFE' ? Math.floor(Math.random() * 80) + 30 :
                           Math.floor(Math.random() * 200) + 50;

          // Calculate discounted price
          const discountValue = promo.discountValue || 0;
          const discountedPrice = promo.discountType === 'PERCENTAGE'
            ? basePrice * (1 - discountValue / 100)
            : basePrice - discountValue;

          return {
            id: promo.id,
            name: promo.title,
            description: promo.description,
            price: basePrice,
            discountedPrice: Math.max(discountedPrice, 0), // Ensure price doesn't go negative
            discountValue: promo.discountValue,
            discountType: promo.discountType,
            currency: 'ZMW',
            imageUrl: promo.imageUrl, // Only real images, no fallbacks
          };
        }),
    }));

    return NextResponse.json({
      partners: transformedPartners,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: Math.floor(offset / limit) + 1,
        hasNextPage: offset + limit < totalCount,
        hasPrevPage: offset > 0,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error fetching featured partners:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured partners' },
      { status: 500 }
    );
  }
}
