'use client';

import { But<PERSON> } from '@/components/ui/button'
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star, MapPin, BadgeCheck, Loader2, Store, Phone, Globe, Package } from 'lucide-react'
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface Vendor {
  id: string;
  businessName: string;
  businessType?: string;
  description?: string;
  location?: string;
  rating?: number;
  totalReviews: number;
  totalSales: number;
  totalOrders: number;
  featured: boolean;
  verified: boolean;
  phoneNumber?: string;
  email?: string;
  website?: string;
  logo?: string;
  bannerImage?: string;
  productCategories?: string;
  serviceCategories?: string;
  specializations?: string;
  acceptedPaymentMethods?: string;
  businessHours?: any;
  featuring?: {
    tier: string;
    endDate: string;
    badge: string;
    badgeColor: string;
  } | null;
  user: {
    id: string;
    name: string;
    image?: string;
  };
  products: Array<{
    id: string;
    name: string;
    price: number;
    category: string;
    imagePath?: string;
    imageUrl?: string;
    stockQuantity: number;
  }>;
  productCount: number;
}

export default function VendorListing() {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedBusinessType, setSelectedBusinessType] = useState<string>('');
  const [selectedCity, setSelectedCity] = useState<string>('');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  useEffect(() => {
    fetchVendors();
  }, [selectedBusinessType, selectedCity, showFeaturedOnly]);

  const fetchVendors = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      
      if (selectedBusinessType) {
        params.append('businessType', selectedBusinessType);
      }
      
      if (selectedCity) {
        params.append('city', selectedCity);
      }
      
      if (showFeaturedOnly) {
        params.append('featured', 'true');
      }
      
      params.append('limit', '12'); // Show 12 vendors
      
      const response = await fetch(`/api/vendors/published?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch vendors');
      }
      
      const data = await response.json();
      setVendors(data.vendors || []);
    } catch (err) {
      console.error('Error fetching vendors:', err);
      setError('Failed to load vendors');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-background min-h-screen py-12">
        <div className="container mx-auto px-4">
          <div className="flex justify-center items-center py-20">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading vendors...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-background min-h-screen py-12">
        <div className="container mx-auto px-4">
          <div className="text-center py-20">
            <p className="text-red-500">{error}</p>
            <Button onClick={fetchVendors} className="mt-4">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-background py-12">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold">Featured Vendors</h1>
            <p className="text-muted-foreground mt-1">
              Discover amazing vendors and their products
            </p>
          </div>
          <div className="flex flex-wrap gap-4">
            <select 
              className="px-4 py-2 border rounded-md bg-background"
              value={selectedBusinessType}
              onChange={(e) => setSelectedBusinessType(e.target.value)}
            >
              <option value="">All Business Types</option>
              <option value="Food Service">Food Service</option>
              <option value="Electronics">Electronics</option>
              <option value="Fashion">Fashion</option>
              <option value="Arts & Crafts">Arts & Crafts</option>
              <option value="Beauty">Beauty</option>
              <option value="Home & Garden">Home & Garden</option>
            </select>
            <select 
              className="px-4 py-2 border rounded-md bg-background"
              value={selectedCity}
              onChange={(e) => setSelectedCity(e.target.value)}
            >
              <option value="">All Cities</option>
              <option value="Lusaka">Lusaka</option>
              <option value="Ndola">Ndola</option>
              <option value="Kitwe">Kitwe</option>
              <option value="Livingstone">Livingstone</option>
            </select>
            <Button
              variant={showFeaturedOnly ? "default" : "outline"}
              onClick={() => setShowFeaturedOnly(!showFeaturedOnly)}
            >
              <Star className="h-4 w-4 mr-2" />
              Featured Only
            </Button>
          </div>
        </div>

        {vendors.length === 0 ? (
          <div className="text-center py-20">
            <Store className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No vendors found.</p>
            <p className="text-sm text-muted-foreground mt-2">
              Try adjusting your filters or check back later.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {vendors.map((vendor) => (
              <Card key={vendor.id} className="group hover:shadow-lg transition-shadow overflow-hidden">
                {/* Vendor Header */}
                <div className="relative h-32 bg-gradient-to-r from-blue-500 to-purple-600">
                  {vendor.bannerImage && (
                    <Image
                      src={vendor.bannerImage}
                      alt={`${vendor.businessName} banner`}
                      fill
                      className="object-cover"
                    />
                  )}
                  <div className="absolute inset-0 bg-black/20" />
                  {vendor.featured && (
                    <Badge className="absolute top-2 right-2 bg-yellow-500">
                      <Star className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                </div>

                {/* Vendor Avatar */}
                <div className="relative -mt-8 mb-4 flex justify-center">
                  <div className="w-16 h-16 rounded-full border-4 border-white bg-white overflow-hidden">
                    {vendor.logo || vendor.user.image ? (
                      <Image
                        src={vendor.logo || vendor.user.image || ''}
                        alt={vendor.businessName}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <Store className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>
                </div>

                <CardHeader className="pt-0 text-center">
                  <CardTitle className="text-lg flex items-center justify-center gap-2">
                    {vendor.businessName}
                    {vendor.verified && (
                      <BadgeCheck className="h-4 w-4 text-blue-500" />
                    )}
                  </CardTitle>
                  {vendor.businessType && (
                    <Badge variant="outline" className="mx-auto w-fit">
                      {vendor.businessType}
                    </Badge>
                  )}
                  {vendor.location && (
                    <div className="flex items-center justify-center text-sm text-muted-foreground">
                      <MapPin className="h-3 w-3 mr-1" />
                      {vendor.location}
                    </div>
                  )}
                </CardHeader>

                <CardContent className="space-y-3">
                  {vendor.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {vendor.description}
                    </p>
                  )}

                  {/* Stats */}
                  <div className="flex justify-between text-sm">
                    <div className="text-center">
                      <div className="font-semibold">{vendor.productCount}</div>
                      <div className="text-muted-foreground">Products</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">{vendor.totalOrders}</div>
                      <div className="text-muted-foreground">Orders</div>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center">
                        {vendor.rating ? (
                          <>
                            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 mr-1" />
                            <span className="font-semibold">{vendor.rating.toFixed(1)}</span>
                          </>
                        ) : (
                          <span className="font-semibold">-</span>
                        )}
                      </div>
                      <div className="text-muted-foreground">Rating</div>
                    </div>
                  </div>

                  {/* Sample Products */}
                  {vendor.products.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Sample Products:</h4>
                      <div className="flex gap-2 overflow-x-auto">
                        {vendor.products.slice(0, 3).map((product) => (
                          <div key={product.id} className="flex-shrink-0 w-16 h-16 rounded border overflow-hidden">
                            <Image
                              src={product.imageUrl || product.imagePath || "/api/placeholder/64/64"}
                              alt={product.name}
                              width={64}
                              height={64}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>

                <CardFooter className="flex gap-2">
                  <Button variant="outline" className="flex-1" size="sm">
                    <Package className="h-4 w-4 mr-2" />
                    View Products
                  </Button>
                  <Button
                    className="flex-1"
                    size="sm"
                    onClick={() => window.location.href = `/vendors/${vendor.id}`}
                  >
                    <Store className="h-4 w-4 mr-2" />
                    Visit Store
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
