'use client';

import React, { useRef, useState } from 'react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Camera, Upload, X, Loader2, ImageIcon, Palette } from 'lucide-react';
import { compressImage, validateImageFile, formatFileSize } from '@/utils/imageCompression';

interface BusinessBannerUploaderProps {
  bannerUrl: string | null;
  businessName: string;
  onBannerChange: (url: string) => void;
  onBannerRemove?: () => void;
  disabled?: boolean;
}

export default function BusinessBannerUploader({ 
  bannerUrl, 
  businessName, 
  onBannerChange, 
  onBannerRemove,
  disabled = false
}: BusinessBannerUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(bannerUrl);

  const handleFileUpload = async (file: File) => {
    // Validate file
    const validation = validateImageFile(file, 10 * 1024 * 1024); // 10MB max before compression
    if (!validation.isValid) {
      toast({
        title: 'Invalid file',
        description: validation.error,
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUploading(true);

      // Show compression progress
      toast({
        title: 'Compressing banner image...',
        description: `Original size: ${formatFileSize(file.size)}`,
      });

      // Compress image to 3MB max, optimized for banners (wider aspect ratio)
      const compressionResult = await compressImage(file, {
        maxSizeBytes: 3 * 1024 * 1024, // 3MB for banners
        maxWidth: 1920, // Good for banners
        maxHeight: 1080,
        quality: 0.85, // Good quality for banners
        format: 'jpeg' // JPEG is better for photos/banners
      });

      toast({
        title: 'Banner image compressed',
        description: `Compressed to ${formatFileSize(compressionResult.compressedSize)} (${Math.round(compressionResult.compressionRatio * 100)}% reduction)`,
      });

      // Upload the compressed file
      const formData = new FormData();
      formData.append('file', compressionResult.file);
      formData.append('directory', 'vendor-banners');

      const response = await fetch('/api/upload/vendor-banner', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload banner');
      }

      const data = await response.json();
      const bannerUrl = data.bannerPath || data.imagePath || data.url;
      
      setPreviewUrl(bannerUrl);
      onBannerChange(bannerUrl);

      toast({
        title: 'Banner uploaded successfully',
        description: 'Your business banner has been updated.',
      });
    } catch (error) {
      console.error('Error uploading banner:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload banner',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    await handleFileUpload(file);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      await handleFileUpload(files[0]);
    }
  };

  const handleRemoveBanner = () => {
    setPreviewUrl(null);
    if (onBannerRemove) {
      onBannerRemove();
    }
    onBannerChange('');
    
    toast({
      title: 'Banner removed',
      description: 'Your business banner has been removed.',
    });
  };

  const displayImage = previewUrl || bannerUrl;

  return (
    <div className="space-y-4">
      <Label className="block font-medium text-base">
        Business Banner / Cover Image
      </Label>
      
      <div className="space-y-4">
        {/* Banner Display */}
        <div
          className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors ${
            disabled ? 'cursor-not-allowed opacity-50' : ''
          }`}
          onDragOver={!disabled ? handleDragOver : undefined}
          onDrop={!disabled ? handleDrop : undefined}
          onClick={!disabled ? () => fileInputRef.current?.click() : undefined}
        >
          {displayImage ? (
            // Banner Image Display
            <div className="relative h-48 w-full">
              <Image
                src={displayImage}
                alt={`${businessName} banner`}
                fill
                className="object-cover"
              />
              
              {/* Overlay */}
              <div className={`absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center transition-opacity ${
                isUploading ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
              }`}>
                {isUploading ? (
                  <div className="text-center text-white">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
                    <p className="text-sm">Uploading banner...</p>
                  </div>
                ) : (
                  <div className="text-center text-white">
                    <Camera className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Click to change banner</p>
                  </div>
                )}
              </div>

              {/* Remove button */}
              {!disabled && !isUploading && (
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 h-8 w-8 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveBanner();
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          ) : (
            // Empty State
            <div className="h-48 flex flex-col items-center justify-center text-gray-500 bg-gradient-to-r from-blue-50 to-purple-50">
              {isUploading ? (
                <div className="text-center">
                  <Loader2 className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-500" />
                  <p className="text-lg font-medium">Uploading banner...</p>
                  <p className="text-sm">Please wait while we process your image</p>
                </div>
              ) : (
                <div className="text-center">
                  <div className="bg-gradient-to-br from-blue-500 to-purple-600 p-4 rounded-full mb-4">
                    <ImageIcon className="h-12 w-12 text-white" />
                  </div>
                  <p className="text-lg font-medium text-gray-700 mb-2">Add a banner image</p>
                  <p className="text-sm text-gray-500 mb-4">Drag & drop or click to upload</p>
                  <div className="flex items-center gap-2 text-xs text-gray-400">
                    <Palette className="h-3 w-3" />
                    <span>Recommended: 1920x400px or similar wide format</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Upload Controls */}
        <div className="flex gap-3 justify-center">
          <Button
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={disabled || isUploading}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                {displayImage ? 'Change Banner' : 'Upload Banner'}
              </>
            )}
          </Button>

          {displayImage && !disabled && (
            <Button
              variant="outline"
              onClick={handleRemoveBanner}
              disabled={isUploading}
            >
              <X className="h-4 w-4 mr-2" />
              Remove Banner
            </Button>
          )}
        </div>

        {/* File Requirements */}
        <div className="text-center space-y-1">
          <p className="text-xs text-muted-foreground">
            JPG, PNG up to 10MB • Automatically compressed to 3MB
          </p>
          <p className="text-xs text-muted-foreground">
            Wide images (16:9 or 21:9 aspect ratio) work best for banners
          </p>
        </div>

        {/* Hidden File Input */}
        <input
          type="file"
          accept="image/*"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileChange}
          disabled={disabled}
        />
      </div>
    </div>
  );
}
