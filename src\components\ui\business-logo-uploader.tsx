'use client';

import React, { useRef, useState } from 'react';
import Image from 'next/image';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Camera, Upload, X, Loader2, Store, Building } from 'lucide-react';
import { compressImage, validateImageFile, formatFileSize } from '@/utils/imageCompression';

interface BusinessLogoUploaderProps {
  logoUrl: string | null;
  businessName: string;
  onLogoChange: (url: string) => void;
  onLogoRemove?: () => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export default function BusinessLogoUploader({ 
  logoUrl, 
  businessName, 
  onLogoChange, 
  onLogoRemove,
  disabled = false,
  size = 'lg'
}: BusinessLogoUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(logoUrl);

  // Size configurations
  const sizeConfig = {
    sm: { avatar: 'h-16 w-16', icon: 'h-4 w-4', text: 'text-sm' },
    md: { avatar: 'h-24 w-24', icon: 'h-6 w-6', text: 'text-base' },
    lg: { avatar: 'h-32 w-32', icon: 'h-8 w-8', text: 'text-lg' }
  };

  const config = sizeConfig[size];

  // Get business initials for fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleFileUpload = async (file: File) => {
    // Validate file
    const validation = validateImageFile(file, 10 * 1024 * 1024); // 10MB max before compression
    if (!validation.isValid) {
      toast({
        title: 'Invalid file',
        description: validation.error,
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsUploading(true);

      // Show compression progress
      toast({
        title: 'Compressing image...',
        description: `Original size: ${formatFileSize(file.size)}`,
      });

      // Compress image to 2MB max, optimized for logos
      const compressionResult = await compressImage(file, {
        maxSizeBytes: 2 * 1024 * 1024, // 2MB
        maxWidth: 800, // Good for logos
        maxHeight: 800,
        quality: 0.9, // Higher quality for logos
        format: file.type.includes('png') ? 'png' : 'jpeg' // Preserve PNG for transparency
      });

      toast({
        title: 'Image compressed',
        description: `Compressed to ${formatFileSize(compressionResult.compressedSize)} (${Math.round(compressionResult.compressionRatio * 100)}% reduction)`,
      });

      // Upload the compressed file
      const formData = new FormData();
      formData.append('file', compressionResult.file);
      formData.append('directory', 'vendor-logos');

      const response = await fetch('/api/upload/vendor-logo', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to upload logo');
      }

      const data = await response.json();
      const logoUrl = data.logoPath || data.imagePath || data.url;
      
      setPreviewUrl(logoUrl);
      onLogoChange(logoUrl);

      toast({
        title: 'Logo uploaded successfully',
        description: 'Your business logo has been updated.',
      });
    } catch (error) {
      console.error('Error uploading logo:', error);
      toast({
        title: 'Upload failed',
        description: error instanceof Error ? error.message : 'Failed to upload logo',
        variant: 'destructive',
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    await handleFileUpload(file);
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    
    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      await handleFileUpload(files[0]);
    }
  };

  const handleRemoveLogo = () => {
    setPreviewUrl(null);
    if (onLogoRemove) {
      onLogoRemove();
    }
    onLogoChange('');
    
    toast({
      title: 'Logo removed',
      description: 'Your business logo has been removed.',
    });
  };

  const displayImage = previewUrl || logoUrl;
  const initials = getInitials(businessName || 'Business');

  return (
    <div className="space-y-4">
      <Label className={`block font-medium ${config.text}`}>
        Business Logo
      </Label>
      
      <div className="flex flex-col items-center space-y-4">
        {/* Logo Display */}
        <div
          className={`relative group cursor-pointer ${disabled ? 'cursor-not-allowed opacity-50' : ''}`}
          onDragOver={!disabled ? handleDragOver : undefined}
          onDrop={!disabled ? handleDrop : undefined}
          onClick={!disabled ? () => fileInputRef.current?.click() : undefined}
        >
          <Avatar className={`${config.avatar} border-2 border-gray-200 shadow-lg`}>
            <AvatarImage src={displayImage || undefined} alt={`${businessName} logo`} />
            <AvatarFallback className={`${config.text} bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold`}>
              {displayImage ? <Building className={config.icon} /> : initials}
            </AvatarFallback>
          </Avatar>

          {/* Overlay */}
          <div className={`absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center transition-opacity ${
            isUploading ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
          }`}>
            {isUploading ? (
              <Loader2 className={`${config.icon} text-white animate-spin`} />
            ) : (
              <Camera className={`${config.icon} text-white`} />
            )}
          </div>

          {/* Remove button */}
          {displayImage && !disabled && !isUploading && (
            <Button
              variant="destructive"
              size="sm"
              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveLogo();
              }}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Upload Instructions */}
        <div className="text-center space-y-2">
          <p className={`text-muted-foreground ${size === 'sm' ? 'text-xs' : 'text-sm'}`}>
            {isUploading ? 'Uploading logo...' : 
             displayImage ? 'Click to change logo' : 'Click or drag to upload business logo'}
          </p>

          {/* Upload Button */}
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              size={size === 'sm' ? 'sm' : 'default'}
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  {displayImage ? 'Change Logo' : 'Upload Logo'}
                </>
              )}
            </Button>

            {displayImage && !disabled && (
              <Button
                variant="outline"
                size={size === 'sm' ? 'sm' : 'default'}
                onClick={handleRemoveLogo}
                disabled={isUploading}
              >
                <X className="h-4 w-4 mr-2" />
                Remove
              </Button>
            )}
          </div>

          {/* File Requirements */}
          <p className="text-xs text-muted-foreground">
            PNG, JPG up to 10MB • Automatically compressed to 2MB
          </p>
          
          {size === 'lg' && (
            <p className="text-xs text-muted-foreground">
              Square images work best for logos • Transparent backgrounds supported
            </p>
          )}
        </div>

        {/* Hidden File Input */}
        <input
          type="file"
          accept="image/*"
          ref={fileInputRef}
          className="hidden"
          onChange={handleFileChange}
          disabled={disabled}
        />
      </div>
    </div>
  );
}
