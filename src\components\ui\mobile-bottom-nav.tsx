'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { Home, Ticket, Calendar, User, Wallet, Hotel } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useCurrentRole } from '@/hooks/use-current-role';

export function MobileBottomNav() {
  const pathname = usePathname();
  const role = useCurrentRole();

  // Get role-specific profile URL
  const getProfileUrl = () => {
    switch (role) {
      case 'VENDOR':
        return '/dashboard/vendor/profile';
      case 'ORGANIZER':
        return '/dashboard/organizer/profile';
      case 'ADMIN':
      case 'SUPERADMIN':
        return '/dashboard/admin/profile';
      case 'PARTNER':
        return '/dashboard/partner/profile';
      default:
        return '/dashboard/user/profile';
    }
  };

  const navItems = [
    {
      label: 'Home',
      href: '/',
      icon: Home,
    },
    {
      label: 'Events',
      href: '/events',
      icon: Calendar,
    },
    {
      label: 'Partners',
      href: '/partners',
      icon: Hotel,
    },
    {
      label: 'Tickets',
      href: '/my-tickets',
      icon: Ticket,
    },
    {
      label: 'NFC',
      href: '/nfc-store',
      icon: Wallet,
    },
    {
      label: 'Profile',
      href: getProfileUrl(),
      icon: User,
    },
  ];

  // Don't show bottom nav on dashboard pages
  if (pathname && pathname.includes('/dashboard') && !pathname.includes('/dashboard/wallet')) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t md:hidden">
      <div className="flex justify-around items-center h-16">
        {navItems.map((item) => {
          const isActive =
            pathname === item.href ||
            (item.href !== '/' && pathname?.startsWith(item.href));

          const Icon = item.icon;

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                'flex flex-col items-center justify-center w-full h-full',
                isActive
                  ? 'text-primary'
                  : 'text-gray-500 hover:text-gray-900'
              )}
            >
              <Icon className="h-5 w-5" />
              <span className="text-xs mt-1">{item.label}</span>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
