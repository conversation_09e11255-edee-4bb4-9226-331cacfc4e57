'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Star,
  MapPin,
  Phone,
  Globe,
  CheckCircle2,
  Package,
  Users,
  Calendar,
  ArrowRight,
  Crown
} from 'lucide-react';

interface FeaturedPartner {
  id: string;
  businessName: string;
  partnerType: string;
  tier: string;
  description?: string;
  city: string;
  province: string;
  logo?: string;
  bannerImage?: string;
  rating?: number;
  totalReviews: number;
  isVerified: boolean;
  featured: boolean;
  acceptsNfcPayments: boolean;
  website?: string;
  contactPhone: string;
  products: Array<{
    id: string;
    name: string;
    type: string;
    price: number;
    currency: string;
    imageUrl?: string;
  }>;
  _count: {
    products: number;
    reviews: number;
    promotions: number;
  };
}

interface FeaturedPartnersSectionProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

const getPartnerTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'hotel':
      return '🏨';
    case 'restaurant':
      return '🍽️';
    case 'bar':
      return '🍺';
    case 'nightclub':
      return '🎵';
    case 'cafe':
      return '☕';
    case 'lounge':
      return '🛋️';
    case 'venue':
      return '🏛️';
    case 'catering':
      return '🍴';
    case 'transport':
      return '🚗';
    case 'entertainment':
      return '🎭';
    case 'retail':
      return '🛍️';
    case 'service':
      return '⚙️';
    default:
      return '🏢';
  }
};

const formatPrice = (price: number, currency: string = 'ZMW') => {
  return new Intl.NumberFormat('en-ZM', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
};

export function FeaturedPartnersSection({ 
  limit = 6, 
  showHeader = true, 
  className = '' 
}: FeaturedPartnersSectionProps) {
  const [partners, setPartners] = useState<FeaturedPartner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedPartners = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/admin/partners/featured?limit=${limit}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch featured partners');
        }
        
        const data = await response.json();
        setPartners(data.partners || []);
      } catch (err) {
        console.error('Error fetching featured partners:', err);
        setError('Failed to load featured partners');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedPartners();
  }, [limit]);

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        {showHeader && (
          <div className="text-center">
            <Skeleton className="h-8 w-64 mx-auto mb-2" />
            <Skeleton className="h-4 w-96 mx-auto" />
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: limit }).map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <Skeleton className="h-48 w-full" />
              <CardContent className="p-4 space-y-3">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-20" />
                  <Skeleton className="h-8 w-16" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-red-500 mb-4">⚠️</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Featured Partners</h3>
        <p className="text-gray-500">{error}</p>
      </div>
    );
  }

  if (partners.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Crown className="h-16 w-16 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Featured Partners</h3>
        <p className="text-gray-500">Check back later for featured partners and exclusive offers.</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {showHeader && (
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Featured Partners
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Discover our premium partners offering exceptional products, services, and experiences
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {partners.map((partner) => (
          <Card key={partner.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-0 shadow-lg">
            {/* Partner Header with Banner */}
            <div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600">
              {partner.bannerImage ? (
                <Image
                  src={partner.bannerImage}
                  alt={`${partner.businessName} banner`}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600" />
              )}
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-black/20" />
              
              {/* Featured Badge */}
              <div className="absolute top-3 left-3">
                <Badge className="bg-yellow-500 text-white font-medium">
                  <Crown className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              </div>

              {/* Tier Badge */}
              <div className="absolute top-3 right-3">
                <Badge 
                  className={`font-medium ${
                    partner.tier === 'ELITE' ? 'bg-purple-600 text-white' :
                    partner.tier === 'PREMIUM' ? 'bg-blue-600 text-white' :
                    'bg-gray-600 text-white'
                  }`}
                >
                  {partner.tier}
                </Badge>
              </div>

              {/* Partner Logo */}
              <div className="absolute -bottom-8 left-4">
                <div className="w-16 h-16 rounded-full border-4 border-white bg-white overflow-hidden shadow-lg">
                  {partner.logo ? (
                    <Image
                      src={partner.logo}
                      alt={partner.businessName}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg">
                      {partner.businessName.charAt(0)}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <CardContent className="pt-12 p-6">
              {/* Partner Info */}
              <div className="space-y-4">
                {/* Name and Type */}
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-bold text-lg text-gray-900 line-clamp-1">
                      {partner.businessName}
                    </h3>
                    {partner.isVerified && (
                      <CheckCircle2 className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>{getPartnerTypeIcon(partner.partnerType)}</span>
                    <span className="capitalize">{partner.partnerType.toLowerCase()}</span>
                    <span>•</span>
                    <MapPin className="h-3 w-3" />
                    <span>{partner.city}, {partner.province}</span>
                  </div>
                </div>

                {/* Description */}
                {partner.description && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {partner.description}
                  </p>
                )}

                {/* Rating */}
                {partner.rating && partner.rating > 0 && (
                  <div className="flex items-center gap-2">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium ml-1">
                        {partner.rating.toFixed(1)}
                      </span>
                    </div>
                    <span className="text-xs text-gray-500">
                      ({partner.totalReviews} {partner.totalReviews === 1 ? 'review' : 'reviews'})
                    </span>
                  </div>
                )}

                {/* Stats */}
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Package className="h-3 w-3" />
                    <span>{partner._count.products} products</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>{partner._count.reviews} reviews</span>
                  </div>
                  {partner.acceptsNfcPayments && (
                    <Badge variant="outline" className="text-xs">
                      NFC Pay
                    </Badge>
                  )}
                </div>

                {/* Sample Products */}
                {partner.products.length > 0 && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Featured Products:</h4>
                    <div className="flex gap-2 overflow-x-auto">
                      {partner.products.slice(0, 3).map((product) => (
                        <div key={product.id} className="flex-shrink-0 min-w-0">
                          <div className="w-16 h-16 rounded border overflow-hidden bg-gray-100">
                            {product.imageUrl ? (
                              <Image
                                src={product.imageUrl}
                                alt={product.name}
                                width={64}
                                height={64}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <Package className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <p className="text-xs text-gray-600 mt-1 truncate w-16" title={product.name}>
                            {product.name}
                          </p>
                          <p className="text-xs font-medium text-gray-900">
                            {formatPrice(product.price, product.currency)}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Action Button */}
                <Link href={`/partners/${partner.id}`}>
                  <Button className="w-full group">
                    View Partner
                    <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* View All Button */}
      <div className="text-center pt-6">
        <Link href="/partners">
          <Button variant="outline" size="lg">
            View All Partners
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </Link>
      </div>
    </div>
  );
}
