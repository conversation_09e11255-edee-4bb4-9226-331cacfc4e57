import { db } from '@/lib/prisma';

async function setupFeaturedContent() {
  try {
    console.log('Setting up featured content...');

    // Feature some vendors
    const vendorUpdate = await db.vendorProfile.updateMany({
      where: {
        featured: false,
      },
      data: {
        featured: true,
      },
    });

    // Feature some partners
    const partnerUpdate = await db.partner.updateMany({
      where: {
        featured: false,
        isVerified: true,
      },
      data: {
        featured: true,
      },
    });

    // Note: Events don't have featured field yet, skipping for now
    const eventUpdate = { count: 0 };

    // Get final counts
    const [vendorCount, partnerCount, eventCount] = await Promise.all([
      db.vendorProfile.count({ where: { featured: true } }),
      db.partner.count({ where: { featured: true } }),
      db.event.count(), // Total events since featured field doesn't exist yet
    ]);

    console.log('Featured content setup complete!');
    console.log(`Featured vendors: ${vendorCount}`);
    console.log(`Featured partners: ${partnerCount}`);
    console.log(`Featured events: ${eventCount}`);
    console.log(`Updates made: ${vendorUpdate.count} vendors, ${partnerUpdate.count} partners, ${eventUpdate.count} events`);

  } catch (error) {
    console.error('Error setting up featured content:', error);
  } finally {
    await db.$disconnect();
  }
}

setupFeaturedContent();
