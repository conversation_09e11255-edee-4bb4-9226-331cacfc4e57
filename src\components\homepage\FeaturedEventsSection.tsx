'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Star,
  MapPin,
  Calendar,
  Clock,
  Users,
  Ticket,
  ArrowRight,
  Crown,
  TrendingUp
} from 'lucide-react';

interface FeaturedEvent {
  id: string;
  title: string;
  description?: string;
  startDate: string;
  endDate: string;
  location: string;
  city: string;
  province: string;
  imagePath?: string;
  category: string;
  ticketPrice: number;
  currency: string;
  totalTickets: number;
  soldTickets: number;
  featured: boolean;
  trending?: boolean;
  rating?: number;
  reviewCount?: number;
  organizer: {
    id: string;
    name: string;
    image?: string;
  };
}

interface FeaturedEventsSectionProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

const formatPrice = (price: number, currency: string = 'ZMW') => {
  return new Intl.NumberFormat('en-ZM', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return {
    day: date.getDate(),
    month: date.toLocaleDateString('en-US', { month: 'short' }),
    weekday: date.toLocaleDateString('en-US', { weekday: 'short' }),
    time: date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  };
};

const getAvailabilityStatus = (totalTickets: number, soldTickets: number) => {
  const remaining = totalTickets - soldTickets;
  const percentageSold = (soldTickets / totalTickets) * 100;
  
  if (remaining === 0) return { status: 'sold-out', text: 'Sold Out', color: 'bg-red-500' };
  if (percentageSold >= 90) return { status: 'almost-sold', text: 'Almost Sold Out', color: 'bg-orange-500' };
  if (percentageSold >= 75) return { status: 'selling-fast', text: 'Selling Fast', color: 'bg-yellow-500' };
  return { status: 'available', text: `${remaining} tickets left`, color: 'bg-green-500' };
};

export function FeaturedEventsSection({ 
  limit = 6, 
  showHeader = true, 
  className = '' 
}: FeaturedEventsSectionProps) {
  const [events, setEvents] = useState<FeaturedEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedEvents = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/events/featured?limit=${limit}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch featured events');
        }
        
        const data = await response.json();
        setEvents(data.events || []);
      } catch (err) {
        console.error('Error fetching featured events:', err);
        setError('Failed to load featured events');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedEvents();
  }, [limit]);

  if (loading) {
    return (
      <section className={`py-16 bg-gradient-to-br from-blue-50 to-purple-50 ${className}`}>
        <div className="container mx-auto px-4">
          {showHeader && (
            <div className="text-center mb-12">
              <Skeleton className="h-10 w-64 mx-auto mb-4" />
              <Skeleton className="h-6 w-96 mx-auto" />
            </div>
          )}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: limit }).map((_, index) => (
              <Card key={index} className="overflow-hidden">
                <Skeleton className="h-48 w-full" />
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                  <div className="flex gap-2">
                    <Skeleton className="h-8 w-20" />
                    <Skeleton className="h-8 w-16" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className={`py-16 bg-gradient-to-br from-blue-50 to-purple-50 ${className}`}>
        <div className="container mx-auto px-4 text-center">
          <div className="text-red-500 mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Featured Events</h3>
          <p className="text-gray-500">{error}</p>
        </div>
      </section>
    );
  }

  if (events.length === 0) {
    return (
      <section className={`py-16 bg-gradient-to-br from-blue-50 to-purple-50 ${className}`}>
        <div className="container mx-auto px-4 text-center">
          <Calendar className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Featured Events</h3>
          <p className="text-gray-500">Check back later for exciting featured events.</p>
        </div>
      </section>
    );
  }

  return (
    <section className={`py-16 bg-gradient-to-br from-blue-50 to-purple-50 ${className}`}>
      <div className="container mx-auto px-4">
        {showHeader && (
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Crown className="h-8 w-8 text-yellow-500" />
              <h2 className="text-4xl font-bold text-gray-900">Featured Events</h2>
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the most exciting events happening right now. Don't miss out on these amazing experiences!
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {events.map((event) => {
            const startDate = formatDate(event.startDate);
            const availability = getAvailabilityStatus(event.totalTickets, event.soldTickets);
            
            return (
              <Card key={event.id} className="group hover:shadow-2xl transition-all duration-300 overflow-hidden border-0 shadow-lg bg-white">
                {/* Event Image */}
                <div className="relative h-56 overflow-hidden">
                  {event.imagePath ? (
                    <Image
                      src={event.imagePath}
                      alt={event.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <Calendar className="h-16 w-16 text-white opacity-50" />
                    </div>
                  )}
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/20" />
                  
                  {/* Badges */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2">
                    <Badge className="bg-yellow-500 text-white font-medium">
                      <Crown className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                    {event.trending && (
                      <Badge className="bg-red-500 text-white font-medium">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Trending
                      </Badge>
                    )}
                  </div>

                  {/* Date Badge */}
                  <div className="absolute top-4 right-4 bg-white rounded-lg p-3 text-center shadow-lg">
                    <div className="text-2xl font-bold text-gray-900">{startDate.day}</div>
                    <div className="text-xs font-medium text-gray-600 uppercase">{startDate.month}</div>
                  </div>

                  {/* Availability Status */}
                  <div className="absolute bottom-4 left-4">
                    <Badge className={`${availability.color} text-white font-medium`}>
                      <Ticket className="h-3 w-3 mr-1" />
                      {availability.text}
                    </Badge>
                  </div>
                </div>

                <CardContent className="p-6">
                  {/* Event Info */}
                  <div className="space-y-4">
                    {/* Category */}
                    <Badge variant="outline" className="text-xs">
                      {event.category}
                    </Badge>

                    {/* Title */}
                    <h3 className="font-bold text-xl text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
                      {event.title}
                    </h3>

                    {/* Description */}
                    {event.description && (
                      <p className="text-gray-600 text-sm line-clamp-2">
                        {event.description}
                      </p>
                    )}

                    {/* Event Details */}
                    <div className="space-y-2 text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>{startDate.weekday}, {startDate.month} {startDate.day}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>{startDate.time}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span className="line-clamp-1">{event.location}, {event.city}</span>
                      </div>
                    </div>

                    {/* Rating */}
                    {event.rating && event.rating > 0 && (
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium ml-1">
                            {event.rating.toFixed(1)}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">
                          ({event.reviewCount} reviews)
                        </span>
                      </div>
                    )}

                    {/* Price and CTA */}
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div>
                        <div className="text-2xl font-bold text-gray-900">
                          {formatPrice(event.ticketPrice, event.currency)}
                        </div>
                        <div className="text-xs text-gray-500">per ticket</div>
                      </div>
                      
                      <Link href={`/events/${event.id}`}>
                        <Button className="group/btn">
                          Get Tickets
                          <ArrowRight className="h-4 w-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                        </Button>
                      </Link>
                    </div>

                    {/* Organizer */}
                    <div className="flex items-center gap-3 pt-2 border-t">
                      <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-100">
                        {event.organizer.image ? (
                          <Image
                            src={event.organizer.image}
                            alt={event.organizer.name}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-xs font-bold">
                            {event.organizer.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="text-xs text-gray-500">Organized by</p>
                        <p className="text-sm font-medium text-gray-900">{event.organizer.name}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* View All Events Button */}
        <div className="text-center mt-12">
          <Link href="/events">
            <Button variant="outline" size="lg" className="group">
              View All Events
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
