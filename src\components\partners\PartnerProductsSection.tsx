'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Star,
  Package,
  Wrench,
  Gift,
  MapPin,
  Clock,
  Users,
  Tag,
  Heart,
  Share2,
  ShoppingCart,
  Eye,
  Calendar,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface PartnerProduct {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  type: 'PRODUCT' | 'SERVICE' | 'PACKAGE' | 'EXPERIENCE';
  category: string;
  subcategory?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  imageUrl?: string;
  galleryImages?: any;
  status: string;
  isAvailable: boolean;
  isFeatured: boolean;
  isPopular: boolean;
  stockQuantity?: number;
  duration?: number;
  durationUnit?: string;
  location?: string;
  inclusions: string[];
  exclusions: string[];
  requirements: string[];
  tags: string[];
  rating?: number;
  totalReviews: number;
  totalSales: number;
  viewCount: number;
  createdAt: string;
  reviews: Array<{
    rating: number;
  }>;
}

interface PartnerProductsSectionProps {
  partnerId: string;
  partnerName: string;
  products: PartnerProduct[];
}

const getProductTypeIcon = (type: string) => {
  switch (type) {
    case 'PRODUCT':
      return <Package className="h-4 w-4" />;
    case 'SERVICE':
      return <Wrench className="h-4 w-4" />;
    case 'PACKAGE':
      return <Gift className="h-4 w-4" />;
    case 'EXPERIENCE':
      return <Calendar className="h-4 w-4" />;
    default:
      return <Package className="h-4 w-4" />;
  }
};

const formatPrice = (price: number, currency: string = 'ZMW') => {
  return new Intl.NumberFormat('en-ZM', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(price);
};

const formatDuration = (duration?: number, unit?: string) => {
  if (!duration || !unit) return null;
  return `${duration} ${unit}${duration > 1 ? 's' : ''}`;
};

export function PartnerProductsSection({ partnerId, partnerName, products }: PartnerProductsSectionProps) {
  const [selectedType, setSelectedType] = useState<string>('all');

  // Group products by type
  const productsByType = products.reduce((acc, product) => {
    if (!acc[product.type]) {
      acc[product.type] = [];
    }
    acc[product.type].push(product);
    return acc;
  }, {} as Record<string, PartnerProduct[]>);

  // Filter products based on selected type
  const filteredProducts = selectedType === 'all' 
    ? products 
    : products.filter(product => product.type === selectedType);

  // Get unique types for tabs
  const availableTypes = ['all', ...Object.keys(productsByType)];

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="h-16 w-16 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Products or Services</h3>
        <p className="text-gray-500">This partner hasn't added any products or services yet.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Products & Services</h2>
          <p className="text-gray-600 mt-1">
            Discover what {partnerName} has to offer
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {products.length} {products.length === 1 ? 'Item' : 'Items'}
        </Badge>
      </div>

      {/* Type Filter Tabs */}
      <Tabs value={selectedType} onValueChange={setSelectedType} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            All ({products.length})
          </TabsTrigger>
          {Object.entries(productsByType).map(([type, items]) => (
            <TabsTrigger key={type} value={type} className="flex items-center gap-2">
              {getProductTypeIcon(type)}
              {type.charAt(0) + type.slice(1).toLowerCase()} ({items.length})
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={selectedType} className="mt-6">
          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <Card key={product.id} className="group hover:shadow-lg transition-shadow overflow-hidden">
                {/* Product Image */}
                <div className="relative h-48 bg-gray-100">
                  {product.imageUrl ? (
                    <Image
                      src={product.imageUrl}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-gradient-to-br from-blue-50 to-purple-50">
                      {getProductTypeIcon(product.type)}
                    </div>
                  )}
                  
                  {/* Badges */}
                  <div className="absolute top-2 left-2 flex flex-col gap-1">
                    {product.isFeatured && (
                      <Badge className="bg-yellow-500 text-white">
                        <Star className="h-3 w-3 mr-1" />
                        Featured
                      </Badge>
                    )}
                    {product.isPopular && (
                      <Badge className="bg-green-500 text-white">
                        <Users className="h-3 w-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                  </div>

                  {/* Type Badge */}
                  <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="text-xs">
                      {getProductTypeIcon(product.type)}
                      <span className="ml-1">{product.type}</span>
                    </Badge>
                  </div>

                  {/* Availability Status */}
                  <div className="absolute bottom-2 right-2">
                    {product.isAvailable ? (
                      <Badge className="bg-green-500 text-white text-xs">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Available
                      </Badge>
                    ) : (
                      <Badge className="bg-red-500 text-white text-xs">
                        <XCircle className="h-3 w-3 mr-1" />
                        Unavailable
                      </Badge>
                    )}
                  </div>
                </div>

                <CardContent className="p-4">
                  {/* Product Info */}
                  <div className="space-y-3">
                    {/* Title and Category */}
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900 line-clamp-1">
                        {product.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        {product.category}
                        {product.subcategory && ` • ${product.subcategory}`}
                      </p>
                    </div>

                    {/* Description */}
                    {product.shortDescription && (
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {product.shortDescription}
                      </p>
                    )}

                    {/* Duration & Location */}
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      {formatDuration(product.duration, product.durationUnit) && (
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatDuration(product.duration, product.durationUnit)}
                        </div>
                      )}
                      {product.location && (
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {product.location}
                        </div>
                      )}
                    </div>

                    {/* Rating */}
                    {product.rating && product.rating > 0 && (
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="text-sm font-medium ml-1">
                            {product.rating.toFixed(1)}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500">
                          ({product.totalReviews} {product.totalReviews === 1 ? 'review' : 'reviews'})
                        </span>
                      </div>
                    )}

                    {/* Price */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-gray-900">
                          {formatPrice(product.price, product.currency)}
                        </span>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <span className="text-sm text-gray-500 line-through">
                            {formatPrice(product.originalPrice, product.currency)}
                          </span>
                        )}
                      </div>
                      
                      {/* Stock Info */}
                      {product.stockQuantity !== null && product.stockQuantity !== undefined && (
                        <span className="text-xs text-gray-500">
                          {product.stockQuantity > 0 ? `${product.stockQuantity} left` : 'Out of stock'}
                        </span>
                      )}
                    </div>

                    {/* Tags */}
                    {product.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {product.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            <Tag className="h-2 w-2 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                        {product.tags.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{product.tags.length - 3} more
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      <Button 
                        size="sm" 
                        className="flex-1"
                        disabled={!product.isAvailable}
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        {product.type === 'SERVICE' ? 'Book Now' : 'Add to Cart'}
                      </Button>
                      <Button size="sm" variant="outline">
                        <Heart className="h-4 w-4" />
                      </Button>
                      <Button size="sm" variant="outline">
                        <Share2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      {/* View All Products Button */}
      {products.length > 12 && (
        <div className="text-center pt-6">
          <Button variant="outline" size="lg">
            View All Products & Services
          </Button>
        </div>
      )}
    </div>
  );
}
