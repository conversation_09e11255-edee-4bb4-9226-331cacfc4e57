import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/vendors/featured
 * Get featured vendors for admin management
 */
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    const [vendors, totalCount] = await Promise.all([
      db.vendorProfile.findMany({
        where: { 
          featured: true,
          verificationStatus: 'APPROVED',
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              products: true,
              reviews: true,
            },
          },
        },
      }),
      db.vendorProfile.count({ 
        where: { 
          featured: true,
          verificationStatus: 'APPROVED',
        },
      }),
    ]);

    return NextResponse.json({
      vendors,
      totalCount,
      hasMore: offset + limit < totalCount,
    });
  } catch (error) {
    console.error('Error fetching featured vendors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured vendors' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/vendors/featured
 * Add/remove vendors from featured list
 */
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { vendorIds, action } = body; // action: 'add' or 'remove'

    if (!vendorIds || !Array.isArray(vendorIds) || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: vendorIds (array) and action' },
        { status: 400 }
      );
    }

    if (!['add', 'remove'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "add" or "remove"' },
        { status: 400 }
      );
    }

    // Update vendors
    const updatedVendors = await db.vendorProfile.updateMany({
      where: {
        id: { in: vendorIds },
        verificationStatus: 'APPROVED', // Only approved vendors can be featured
      },
      data: {
        featured: action === 'add',
      },
    });

    return NextResponse.json({
      message: `Successfully ${action === 'add' ? 'featured' : 'unfeatured'} ${updatedVendors.count} vendor(s)`,
      updatedCount: updatedVendors.count,
    });
  } catch (error) {
    console.error('Error updating featured vendors:', error);
    return NextResponse.json(
      { error: 'Failed to update featured vendors' },
      { status: 500 }
    );
  }
}
