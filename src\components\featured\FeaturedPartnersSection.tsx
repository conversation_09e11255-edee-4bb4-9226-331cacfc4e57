'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import useEmblaCarousel from 'embla-carousel-react';
import {
  Star,
  MapPin,
  Phone,
  Globe,
  CheckCircle2,
  Package,
  Users,
  ArrowRight,
  Crown,
  Building,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Gift,
  Mail
} from 'lucide-react';

interface FeaturedPartner {
  id: string;
  businessName: string;
  partnerType: string;
  tier: string;
  description?: string;
  city: string;
  province: string;
  logo?: string;
  bannerImage?: string;
  rating?: number;
  totalReviews: number;
  isVerified: boolean;
  featured: boolean;
  acceptsNfcPayments: boolean;
  website?: string;
  contactPhone: string;
  email?: string;
  _count?: {
    products: number;
    reviews: number;
    promotions: number;
  };
  currentPromotions: Array<{
    id: string;
    title: string;
    discountValue: number;
    discountType: string;
  }>;
  sampleServices: Array<{
    id: string;
    name: string;
    description?: string;
    price: number;
    discountedPrice?: number;
    discountValue?: number;
    discountType?: string;
    currency: string;
    imageUrl?: string;
    category?: string;
  }>;
}

const getPartnerTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'hotel': return '🏨';
    case 'restaurant': return '🍽️';
    case 'bar': return '🍺';
    case 'nightclub': return '🎵';
    case 'cafe': return '☕';
    case 'lounge': return '🛋️';
    case 'venue': return '🏛️';
    case 'catering': return '🍴';
    case 'transport': return '🚗';
    case 'entertainment': return '🎭';
    case 'retail': return '🛍️';
    case 'service': return '⚙️';
    default: return '🏢';
  }
};

export default function FeaturedPartnersSection() {
  const [partners, setPartners] = useState<FeaturedPartner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: false,
    align: 'start',
    slidesToScroll: 1,
    containScroll: 'trimSnaps',
    dragFree: true,
    breakpoints: {
      '(min-width: 768px)': { slidesToScroll: 1 },
      '(min-width: 1024px)': { slidesToScroll: 1 }
    }
  });

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    const fetchFeaturedPartners = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/partners/featured?limit=12');

        if (!response.ok) {
          throw new Error('Failed to fetch featured partners');
        }

        const data = await response.json();
        setPartners(data.partners || []);
      } catch (err) {
        console.error('Error fetching featured partners:', err);
        setError('Failed to load featured partners');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedPartners();
  }, []);

  if (loading) {
    return (
      <section className="py-16 bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Skeleton className="h-10 w-64 mx-auto mb-4" />
            <Skeleton className="h-6 w-96 mx-auto" />
          </div>
          <div className="relative -mx-4">
            <div className="flex gap-6 overflow-hidden px-4 pb-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="flex-shrink-0 w-[400px] min-w-[400px] overflow-hidden">
                  <Skeleton className="h-32 w-full" />
                  <CardContent className="p-4 space-y-3">
                    <Skeleton className="h-5 w-3/4" />
                    <Skeleton className="h-3 w-full" />
                    <div className="grid grid-cols-2 gap-2">
                      <Skeleton className="h-12 w-full" />
                      <Skeleton className="h-12 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (error || partners.length === 0) {
    return (
      <section className="py-16 bg-gradient-to-br from-purple-50 to-pink-50">
        <div className="container mx-auto px-4 text-center">
          <Building className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {error ? 'Unable to Load Featured Partners' : 'No Featured Partners'}
          </h3>
          <p className="text-gray-500">
            {error || 'Check back later for featured partners and exclusive offers.'}
          </p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-br from-purple-50 to-pink-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Crown className="h-8 w-8 text-yellow-500" />
            <h2 className="text-4xl font-bold text-gray-900">Featured Partners</h2>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Connect with premium partners offering exceptional services, exclusive deals, and unforgettable experiences.
          </p>
        </div>

        {/* Partners Carousel */}
        <div className="relative">
          {/* Left Navigation Arrow */}
          <Button
            variant="outline"
            size="sm"
            onClick={scrollPrev}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-20 h-12 w-12 rounded-full p-0 bg-white/90 backdrop-blur-sm border-purple-200 shadow-lg hover:bg-white hover:shadow-xl hover:border-purple-300 transition-all duration-200 hidden md:flex items-center justify-center"
          >
            <ChevronLeft className="h-5 w-5 text-purple-600" />
          </Button>

          {/* Right Navigation Arrow */}
          <Button
            variant="outline"
            size="sm"
            onClick={scrollNext}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-20 h-12 w-12 rounded-full p-0 bg-white/90 backdrop-blur-sm border-purple-200 shadow-lg hover:bg-white hover:shadow-xl hover:border-purple-300 transition-all duration-200 hidden md:flex items-center justify-center"
          >
            <ChevronRight className="h-5 w-5 text-purple-600" />
          </Button>

          {/* Gradient fade on right to indicate more content */}
          <div className="absolute right-12 top-0 bottom-0 w-16 bg-gradient-to-l from-purple-50 to-transparent z-10 pointer-events-none hidden md:block"></div>

          {/* Gradient fade on left to indicate more content */}
          <div className="absolute left-12 top-0 bottom-0 w-16 bg-gradient-to-r from-purple-50 to-transparent z-10 pointer-events-none hidden md:block"></div>

          <div className="overflow-hidden px-4 md:px-16" ref={emblaRef}>
            <div className="flex gap-6 pb-4">
          {partners.map((partner) => (
            <Card key={partner.id} className="flex-shrink-0 w-[320px] min-w-[320px] sm:w-[300px] sm:min-w-[300px] xs:w-[280px] xs:min-w-[280px] group hover:shadow-lg transition-all duration-300 overflow-hidden border-0 shadow-md bg-white">
              {/* Partner Header with Banner */}
              <div className="relative h-24 bg-gradient-to-br from-purple-500 to-pink-600">
                {partner.bannerImage ? (
                  <Image
                    src={partner.bannerImage}
                    alt={`${partner.businessName} banner`}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600" />
                )}
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/20" />
                
                {/* Featured Badge */}
                <div className="absolute top-4 left-4">
                  <Badge className="bg-yellow-500 text-white font-medium">
                    <Crown className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                </div>

                {/* Tier Badge */}
                <div className="absolute top-4 right-4">
                  <Badge 
                    className={`font-medium ${
                      partner.tier === 'ELITE' ? 'bg-purple-600 text-white' :
                      partner.tier === 'PREMIUM' ? 'bg-blue-600 text-white' :
                      'bg-gray-600 text-white'
                    }`}
                  >
                    {partner.tier}
                  </Badge>
                </div>

                {/* Partner Logo */}
                <div className="absolute -bottom-4 left-4">
                  <div className="w-8 h-8 rounded-full border-2 border-white bg-white overflow-hidden shadow-md">
                    {partner.logo ? (
                      <Image
                        src={partner.logo}
                        alt={partner.businessName}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center text-white font-bold text-lg">
                        {partner.businessName.charAt(0)}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <CardContent className="pt-4 p-4">
                {/* Partner Info */}
                <div className="space-y-1.5">
                  {/* Name and Type */}
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-bold text-base text-gray-900 line-clamp-1">
                        {partner.businessName}
                      </h3>
                      {partner.isVerified && (
                        <CheckCircle2 className="h-5 w-5 text-blue-500" />
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span>{getPartnerTypeIcon(partner.partnerType)}</span>
                      <span className="capitalize">{partner.partnerType.toLowerCase()}</span>
                      <span>•</span>
                      <MapPin className="h-3 w-3" />
                      <span>{partner.city}, {partner.province}</span>
                    </div>
                  </div>

                  {/* Description */}
                  {partner.description && (
                    <p className="text-gray-600 text-sm line-clamp-2">
                      {partner.description}
                    </p>
                  )}

                  {/* Rating */}
                  {partner.rating && partner.rating > 0 && (
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium ml-1">
                          {partner.rating.toFixed(1)}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">
                        ({partner.totalReviews} reviews)
                      </span>
                    </div>
                  )}

                  {/* Stats */}
                  <div className="flex items-center gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Package className="h-3 w-3" />
                      <span>{partner._count?.products || 0} products</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{partner.totalReviews} reviews</span>
                    </div>
                    {partner.acceptsNfcPayments && (
                      <Badge variant="outline" className="text-xs">
                        NFC Pay
                      </Badge>
                    )}
                  </div>



                  {/* Sample Services - Compact Version */}
                  {partner.sampleServices && partner.sampleServices.length > 0 ? (
                    <div>
                      <h4 className="text-sm font-semibold mb-2 text-gray-800 flex items-center gap-2">
                        <Gift className="h-4 w-4 text-purple-600" />
                        Featured Services
                      </h4>
                      <div className={`grid gap-2 ${partner.sampleServices.length === 1 ? 'grid-cols-1' : 'grid-cols-2'}`}>
                        {partner.sampleServices.slice(0, 2).map((service) => (
                          <div key={service.id} className="bg-white rounded-md border border-purple-200 overflow-hidden hover:shadow-sm transition-all duration-200 group/service cursor-pointer">
                            {/* Service Image - Reduced Height */}
                            <div className="aspect-square bg-purple-50 overflow-hidden relative h-16">
                              {service.imageUrl ? (
                                <Image
                                  src={service.imageUrl}
                                  alt={service.name}
                                  width={64}
                                  height={64}
                                  className="w-full h-full object-cover group-hover/service:scale-105 transition-transform duration-200"
                                />
                              ) : (
                                <div className="w-full h-full bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                                  {partner.partnerType === 'HOTEL' ? <Building className="h-6 w-6 text-purple-500" /> :
                                   partner.partnerType === 'RESTAURANT' ? <Gift className="h-6 w-6 text-purple-500" /> :
                                   partner.partnerType === 'BAR' ? <Calendar className="h-6 w-6 text-purple-500" /> :
                                   <Gift className="h-6 w-6 text-purple-500" />}
                                </div>
                              )}

                              {/* Discount Badge */}
                              {service.discountValue && (
                                <div className="absolute top-1 left-1">
                                  <Badge className="bg-red-500 text-white text-xs font-bold">
                                    {service.discountValue}{service.discountType === 'PERCENTAGE' ? '%' : ' ZMW'} OFF
                                  </Badge>
                                </div>
                              )}
                            </div>

                            {/* Service Info - Simplified */}
                            <div className="p-2">
                              <h5 className="text-xs font-medium text-gray-900 line-clamp-1 mb-1 leading-tight" title={service.name}>
                                {service.name}
                              </h5>

                              <div className="flex items-center justify-between">
                                <div className="flex flex-col">
                                  {service.discountedPrice ? (
                                    <div className="flex items-center gap-1">
                                      <span className="text-sm font-bold text-purple-600">
                                        {new Intl.NumberFormat('en-ZM', {
                                          style: 'currency',
                                          currency: service.currency,
                                          minimumFractionDigits: 0,
                                        }).format(service.discountedPrice)}
                                      </span>
                                      <span className="text-xs text-gray-500 line-through">
                                        {new Intl.NumberFormat('en-ZM', {
                                          style: 'currency',
                                          currency: service.currency,
                                          minimumFractionDigits: 0,
                                        }).format(service.price)}
                                      </span>
                                    </div>
                                  ) : (
                                    <span className="text-sm font-bold text-purple-600">
                                      {new Intl.NumberFormat('en-ZM', {
                                        style: 'currency',
                                        currency: service.currency,
                                        minimumFractionDigits: 0,
                                      }).format(service.price)}
                                    </span>
                                  )}
                                </div>

                                <Button size="sm" className="h-6 px-2 text-xs bg-purple-600 hover:bg-purple-700">
                                  Book
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* View More Services - Compact */}
                      <div className="mt-2 text-center">
                        <Button variant="ghost" size="sm" className="text-xs text-purple-600 hover:text-purple-700 h-6">
                          View all services
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-3">
                      <p className="text-xs text-gray-500 mb-1">No featured services</p>
                      <Button variant="ghost" size="sm" className="text-xs text-purple-600 hover:text-purple-700 h-6">
                        View services
                      </Button>
                    </div>
                  )}

                  {/* Action Button - Single Compact Button */}
                  <div className="border-t pt-2">
                    <Link href={`/partners/${partner.id}`}>
                      <Button size="sm" className="w-full group bg-purple-600 hover:bg-purple-700">
                        View Details
                        <ArrowRight className="h-3 w-3 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="flex md:hidden justify-center gap-4 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={scrollPrev}
              className="h-12 w-12 rounded-full p-0 bg-white shadow-lg border-purple-200"
            >
              <ChevronLeft className="h-5 w-5 text-purple-600" />
            </Button>
            <div className="flex items-center gap-2 px-4 py-2 bg-purple-100 rounded-full">
              <span className="text-sm text-purple-700">Swipe to explore</span>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={scrollNext}
              className="h-12 w-12 rounded-full p-0 bg-white shadow-lg border-purple-200"
            >
              <ChevronRight className="h-5 w-5 text-purple-600" />
            </Button>
          </div>
        </div>

        {/* View All Button */}
        <div className="text-center mt-12">
          <Link href="/partners">
            <Button variant="outline" size="lg" className="group">
              View All Partners
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
